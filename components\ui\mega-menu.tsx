"use client";

import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import Link from "next/link";
import Image from "next/image";
import { cn } from "@/lib/utils";
import { 
  Home, 
  Building2, 
  Palette, 
  Code2,
  <PERSON><PERSON><PERSON>,
  <PERSON>rk<PERSON>,
  TrendingUp
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import type { ProductResponse } from "../../@types/product";

// Category configuration
const categories = [
  {
    id: "domestic",
    title: "Domestic",
    description: "Home & Personal",
    icon: Home,
    gradient: "from-blue-500 to-cyan-500",
    iconColor: "text-blue-600",
  },
  {
    id: "commercial",
    title: "Commercial", 
    description: "Business Solutions",
    icon: Building2,
    gradient: "from-green-500 to-emerald-500",
    iconColor: "text-green-600",
  },
  {
    id: "production-creative",
    title: "Production/Creative",
    description: "Creative Tools",
    icon: Palette,
    gradient: "from-purple-500 to-pink-500", 
    iconColor: "text-purple-600",
  },
  {
    id: "custom-development",
    title: "Custom Development",
    description: "Tailored Solutions",
    icon: Code2,
    gradient: "from-orange-500 to-red-500",
    iconColor: "text-orange-600",
  },
];

interface MegaMenuProps {
  products: ProductResponse[];
  loading: boolean;
  isOpen: boolean;
  onClose: () => void;
}

export function MegaMenu({ products, loading, isOpen, onClose }: MegaMenuProps) {
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  // Group products by category
  const groupedProducts = React.useMemo(() => {
    const grouped: Record<string, ProductResponse[]> = {};
    
    categories.forEach(category => {
      grouped[category.id] = products.filter((product: ProductResponse & { category?: string }) => 
        product.category?.toLowerCase() === category.id.toLowerCase()
      ).slice(0, 4); // Limit to 4 products per category
    });
    
    return grouped;
  }, [products]);

  // Featured/Popular products (fallback to first few products)
  const featuredProducts = React.useMemo(() => {
    return products.slice(0, 3);
  }, [products]);

  if (loading) {
    return (
      <div className="w-full p-6">
        <div className="grid grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="space-y-3">
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
              <div className="space-y-2">
                {[...Array(3)].map((_, j) => (
                  <div key={j} className="h-3 bg-gray-100 dark:bg-gray-800 rounded animate-pulse" />
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="w-full p-6">
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Categories Section */}
        <div className="lg:col-span-3">
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-foreground mb-2 flex items-center">
              <Sparkles className="w-5 h-5 mr-2 text-primary" />
              Browse by Category
            </h3>
            <p className="text-sm text-muted-foreground">
              Discover solutions tailored to your specific needs
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-2 gap-6">
            {categories.map((category) => {
              const IconComponent = category.icon;
              const categoryProducts = groupedProducts[category.id] || [];
              
              return (
                <motion.div
                  key={category.id}
                  className={cn(
                    "group relative p-4 rounded-xl border border-border/50 hover:border-primary/30",
                    "bg-gradient-to-br from-background to-muted/20 hover:shadow-lg transition-all duration-300",
                    "cursor-pointer"
                  )}
                  whileHover={{ scale: 1.02 }}
                  onHoverStart={() => setSelectedCategory(category.id)}
                  onHoverEnd={() => setSelectedCategory(null)}
                  onClick={() => {
                    onClose();
                    window.location.href = `/products?category=${category.id}`;
                  }}
                >
                  {/* Category Header */}
                  <div className="flex items-center space-x-3 mb-3">
                    <div className={cn(
                      "w-10 h-10 rounded-lg bg-gradient-to-br flex items-center justify-center",
                      "group-hover:scale-110 transition-transform duration-300",
                      category.gradient
                    )}>
                      <IconComponent className="w-5 h-5 text-white" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-foreground group-hover:text-primary transition-colors">
                        {category.title}
                      </h4>
                      <p className="text-xs text-muted-foreground">
                        {category.description}
                      </p>
                    </div>
                    <ArrowRight className="w-4 h-4 text-muted-foreground group-hover:text-primary group-hover:translate-x-1 transition-all duration-300" />
                  </div>

                  {/* Product Count */}
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">
                      {categoryProducts.length} product{categoryProducts.length !== 1 ? 's' : ''}
                    </span>
                    {categoryProducts.length > 0 && (
                      <Badge variant="secondary" className="text-xs">
                        Available
                      </Badge>
                    )}
                  </div>

                  {/* Hover Effect */}
                  <div className={cn(
                    "absolute inset-0 rounded-xl bg-gradient-to-br opacity-0 group-hover:opacity-10",
                    "transition-opacity duration-300 pointer-events-none",
                    category.gradient
                  )} />
                </motion.div>
              );
            })}
          </div>
        </div>

        {/* Featured Products Sidebar */}
        <div className="lg:col-span-1">
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-foreground mb-2 flex items-center">
              <TrendingUp className="w-5 h-5 mr-2 text-primary" />
              Featured
            </h3>
            <p className="text-sm text-muted-foreground">
              Popular products
            </p>
          </div>

          <div className="space-y-4">
            {featuredProducts.map((product) => (
              <motion.div
                key={product.slug}
                className="group"
                whileHover={{ x: 4 }}
              >
                <Link 
                  href={`/products/${product.slug}`}
                  onClick={onClose}
                  className="flex items-center space-x-3 p-3 rounded-lg hover:bg-muted/50 transition-colors duration-200"
                >
                  <div className="flex-shrink-0 w-10 h-10 rounded-md overflow-hidden bg-gradient-to-br from-primary/10 to-secondary/10">
                    {product.ProductCard.productLogo ? (
                      <Image
                        src={product.ProductCard.productLogo.url}
                        width={40}
                        height={40}
                        alt={product.ProductCard.productName}
                        className="w-full h-full object-contain p-1"
                        onError={(e) => {
                          e.currentTarget.src = "/placeholder.jpg";
                        }}
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <Sparkles className="w-5 h-5 text-muted-foreground" />
                      </div>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <h4 className="text-sm font-medium text-foreground group-hover:text-primary transition-colors line-clamp-1">
                      {product.ProductCard.productName}
                    </h4>
                    <p className="text-xs text-muted-foreground line-clamp-1">
                      {product.ProductCard.productDescription || "Explore this product"}
                    </p>
                  </div>
                </Link>
              </motion.div>
            ))}
          </div>

          {/* View All Products CTA */}
          <div className="mt-6 pt-4 border-t border-border/50">
            <Button
              variant="outline"
              size="sm"
              className="w-full group"
              onClick={() => {
                onClose();
                window.location.href = "/products";
              }}
            >
              View All Products
              <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" />
            </Button>
          </div>
        </div>
      </div>

      {/* Quick Actions Footer */}
      <div className="mt-8 pt-6 border-t border-border/50">
        <div className="flex flex-wrap gap-3 justify-center">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              onClose();
              window.location.href = "/products?category=custom-development";
            }}
            className="group"
          >
            <Code2 className="w-4 h-4 mr-2" />
            Custom Solutions
            <ArrowRight className="ml-2 w-3 h-3 group-hover:translate-x-0.5 transition-transform" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              onClose();
              // Open search dialog
              const searchEvent = new CustomEvent('open-global-search');
              document.dispatchEvent(searchEvent);
            }}
            className="group"
          >
            <Sparkles className="w-4 h-4 mr-2" />
            Search Products
          </Button>
        </div>
      </div>
    </div>
  );
}
