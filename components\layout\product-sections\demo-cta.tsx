"use client";

import React, { useState, useRef } from "react";
import { motion, useInView } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Play,
  Calendar,
  Download,
  ArrowRight,
  CheckCircle,
  Clock,
  Users,
  Star,
  Sparkles,
  Video,
  Phone,
  Mail,
  ExternalLink
} from "lucide-react";
import { cn } from "@/lib/utils";
import SectionContainer from "../section-container";

interface DemoCTAProps {
  title?: string;
  description?: string;
  productName?: string;
  demoVideoUrl?: string;
  calendlyUrl?: string;
  downloadUrl?: string;
  onScheduleDemo?: () => void;
  onWatchDemo?: () => void;
  onDownloadBrochure?: () => void;
}

const demoFeatures = [
  "Live product walkthrough",
  "Personalized use case discussion",
  "Q&A with product experts",
  "Custom implementation planning"
];

const demoStats = [
  { icon: Clock, label: "Demo Duration", value: "30 min" },
  { icon: Users, label: "Attendees", value: "1-10" },
  { icon: Star, label: "Satisfaction", value: "4.9/5" },
];

export function DemoCTA({
  title = "See It in Action",
  description = "Experience the full power of our platform with a personalized demo tailored to your specific needs and use cases.",
  productName = "Our Platform",
  demoVideoUrl = "https://www.youtube.com/embed/demo-video",
  calendlyUrl = "https://calendly.com/your-demo-link",
  downloadUrl = "/brochure.pdf",
  onScheduleDemo,
  onWatchDemo,
  onDownloadBrochure
}: DemoCTAProps) {
  const [isVideoOpen, setIsVideoOpen] = useState(false);
  const [isScheduleOpen, setIsScheduleOpen] = useState(false);
  const [email, setEmail] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const sectionRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(sectionRef, { once: true, margin: "-100px" });

  const handleWatchDemo = () => {
    if (onWatchDemo) {
      onWatchDemo();
    } else {
      setIsVideoOpen(true);
    }
  };

  const handleScheduleDemo = () => {
    if (onScheduleDemo) {
      onScheduleDemo();
    } else {
      setIsScheduleOpen(true);
    }
  };

  const handleDownloadBrochure = () => {
    if (onDownloadBrochure) {
      onDownloadBrochure();
    } else {
      // Trigger download
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = `${productName}-brochure.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const handleQuickDemo = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // In a real implementation, you would send the email to your backend
    console.log('Quick demo requested for:', email);
    
    setIsSubmitting(false);
    setEmail("");
    // Show success message or redirect
  };

  return (
    <SectionContainer ref={sectionRef} id="demo" className="py-16 lg:py-24">
      <div className="max-w-6xl mx-auto">
        {/* Main CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8 }}
          className="text-center space-y-8 mb-16"
        >
          {/* Header */}
          <div className="space-y-4">
            <Badge variant="outline" className="px-4 py-2 text-sm font-medium">
              <Sparkles className="w-4 h-4 mr-2 text-primary" />
              Interactive Demo
            </Badge>
            
            <h2 className="text-4xl lg:text-5xl font-bold text-foreground">
              {title}
            </h2>
            
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              {description}
            </p>
          </div>

          {/* Demo Stats */}
          <div className="flex flex-wrap justify-center gap-8">
            {demoStats.map((stat, index) => {
              const IconComponent = stat.icon;
              return (
                <motion.div
                  key={stat.label}
                  initial={{ opacity: 0, y: 20 }}
                  animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                  transition={{ duration: 0.6, delay: 0.2 + index * 0.1 }}
                  className="flex items-center space-x-3"
                >
                  <IconComponent className="w-5 h-5 text-primary" />
                  <div className="text-left">
                    <div className="font-semibold text-foreground">{stat.value}</div>
                    <div className="text-sm text-muted-foreground">{stat.label}</div>
                  </div>
                </motion.div>
              );
            })}
          </div>
        </motion.div>

        {/* Demo Options Grid */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 40 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="grid md:grid-cols-3 gap-8 mb-16"
        >
          {/* Watch Demo Video */}
          <Card className="group hover:shadow-xl transition-all duration-300 cursor-pointer" onClick={handleWatchDemo}>
            <CardContent className="p-8 text-center space-y-6">
              <div className="w-16 h-16 mx-auto bg-gradient-to-br from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <Play className="w-8 h-8 text-white" />
              </div>
              
              <div className="space-y-3">
                <h3 className="text-xl font-bold text-foreground">Watch Demo Video</h3>
                <p className="text-muted-foreground">
                  Get a quick overview of key features and capabilities in our 5-minute demo video.
                </p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-center space-x-2 text-sm text-muted-foreground">
                  <Video className="w-4 h-4" />
                  <span>5 min video</span>
                </div>
                <div className="flex items-center justify-center space-x-2 text-sm text-muted-foreground">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span>No registration required</span>
                </div>
              </div>

              <Button className="w-full group/btn">
                Watch Now
                <Play className="ml-2 w-4 h-4 group-hover/btn:scale-110 transition-transform" />
              </Button>
            </CardContent>
          </Card>

          {/* Schedule Live Demo */}
          <Card className="group hover:shadow-xl transition-all duration-300 cursor-pointer border-primary/50 bg-gradient-to-br from-primary/5 to-primary/10" onClick={handleScheduleDemo}>
            <CardContent className="p-8 text-center space-y-6">
              <div className="w-16 h-16 mx-auto bg-gradient-to-br from-primary to-primary/80 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <Calendar className="w-8 h-8 text-primary-foreground" />
              </div>
              
              <div className="space-y-3">
                <div className="flex items-center justify-center space-x-2">
                  <h3 className="text-xl font-bold text-foreground">Schedule Live Demo</h3>
                  <Badge className="bg-primary/20 text-primary">Recommended</Badge>
                </div>
                <p className="text-muted-foreground">
                  Book a personalized 30-minute session with our product experts.
                </p>
              </div>

              <div className="space-y-2">
                {demoFeatures.map((feature, index) => (
                  <div key={feature} className="flex items-center space-x-2 text-sm text-muted-foreground">
                    <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                    <span>{feature}</span>
                  </div>
                ))}
              </div>

              <Button className="w-full group/btn">
                Schedule Demo
                <Calendar className="ml-2 w-4 h-4 group-hover/btn:scale-110 transition-transform" />
              </Button>
            </CardContent>
          </Card>

          {/* Download Resources */}
          <Card className="group hover:shadow-xl transition-all duration-300 cursor-pointer" onClick={handleDownloadBrochure}>
            <CardContent className="p-8 text-center space-y-6">
              <div className="w-16 h-16 mx-auto bg-gradient-to-br from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <Download className="w-8 h-8 text-white" />
              </div>
              
              <div className="space-y-3">
                <h3 className="text-xl font-bold text-foreground">Download Brochure</h3>
                <p className="text-muted-foreground">
                  Get detailed product information, pricing, and implementation guides.
                </p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-center space-x-2 text-sm text-muted-foreground">
                  <Download className="w-4 h-4" />
                  <span>PDF Download</span>
                </div>
                <div className="flex items-center justify-center space-x-2 text-sm text-muted-foreground">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span>Instant access</span>
                </div>
              </div>

              <Button variant="outline" className="w-full group/btn">
                Download Now
                <Download className="ml-2 w-4 h-4 group-hover/btn:translate-y-1 transition-transform" />
              </Button>
            </CardContent>
          </Card>
        </motion.div>

        {/* Quick Demo Request */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="bg-gradient-to-r from-muted/50 to-muted/30 rounded-2xl p-8 text-center"
        >
          <div className="max-w-2xl mx-auto space-y-6">
            <div className="space-y-2">
              <h3 className="text-2xl font-bold text-foreground">
                Get a Quick Demo Link
              </h3>
              <p className="text-muted-foreground">
                Enter your email to receive an instant demo link and get started right away.
              </p>
            </div>

            <form onSubmit={handleQuickDemo} className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <div className="flex-1">
                <Label htmlFor="demo-email" className="sr-only">Email address</Label>
                <Input
                  id="demo-email"
                  type="email"
                  placeholder="Enter your email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  className="w-full"
                />
              </div>
              <Button type="submit" disabled={isSubmitting} className="group">
                {isSubmitting ? (
                  "Sending..."
                ) : (
                  <>
                    Get Demo Link
                    <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" />
                  </>
                )}
              </Button>
            </form>

            <p className="text-xs text-muted-foreground">
              No spam, unsubscribe at any time. By submitting, you agree to our privacy policy.
            </p>
          </div>
        </motion.div>
      </div>

      {/* Video Modal */}
      <Dialog open={isVideoOpen} onOpenChange={setIsVideoOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] p-0">
          <DialogHeader className="p-6 pb-0">
            <DialogTitle>Product Demo Video</DialogTitle>
            <DialogDescription>
              Watch our comprehensive product demonstration
            </DialogDescription>
          </DialogHeader>
          
          <div className="aspect-video p-6">
            <iframe
              src={demoVideoUrl}
              title="Product Demo"
              className="w-full h-full rounded-lg"
              allowFullScreen
            />
          </div>
        </DialogContent>
      </Dialog>

      {/* Schedule Modal */}
      <Dialog open={isScheduleOpen} onOpenChange={setIsScheduleOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] p-0">
          <DialogHeader className="p-6 pb-0">
            <DialogTitle>Schedule Your Demo</DialogTitle>
            <DialogDescription>
              Choose a time that works best for you
            </DialogDescription>
          </DialogHeader>
          
          <div className="flex-1 min-h-[500px] p-6">
            <iframe
              src={calendlyUrl}
              width="100%"
              height="500"
              frameBorder="0"
              title="Schedule Demo"
            />
          </div>
        </DialogContent>
      </Dialog>
    </SectionContainer>
  );
}
