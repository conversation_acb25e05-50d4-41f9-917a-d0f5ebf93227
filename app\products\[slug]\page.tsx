"use client";
import { HeroSection } from "@/components/layout/product-sections/hero";
// import { EnhancedHeroSection } from "@/components/layout/product-sections/enhanced-hero";
// import { ScreenshotsGallery } from "@/components/layout/product-sections/screenshots-gallery";
// import { FeatureTabs } from "@/components/layout/product-sections/feature-tabs";
// import { DemoCTA } from "@/components/layout/product-sections/demo-cta";
import { useEffect, useState, useRef } from "react";
import { usePathname, useSearchParams } from "next/navigation";
import { getHeroContentBySlug } from "@/app/api/heroSection";
import { getBenefitsContentBySlug } from "@/app/api/benefitsSection";
import { getFeaturesContentBySlug } from "@/app/api/featuresSection";
import { getServicesContentBySlug } from "@/app/api/servicesSection";
import { getFaqContentBySlug } from "@/app/api/faqSection";
import { getPricingContentBySlug } from "@/app/api/pricingSection";
import { RichTextNode } from "@/types/richTextNode";

import { BenefitsSection } from "@/components/layout/product-sections/benefits";
// import { CommunitySection } from "@/components/layout/product-sections/community";
import { ContactSection } from "@/components/layout/product-sections/contact";
import { FAQSection } from "@/components/layout/product-sections/faq";
import { FeaturesSection } from "@/components/layout/product-sections/features";
import { FooterSection } from "@/components/layout/product-sections/footer";
import { NewsletterSection } from "@/components/layout/product-sections/newsletter";
import { PricingSection } from "@/components/layout/product-sections/pricing";
import { ServicesSection } from "@/components/layout/product-sections/services";
// import { SponsorsSection } from "@/components/layout/product-sections/sponsors";
import { TestimonialSection } from "@/components/layout/product-sections/testimonial";
import { useSession } from "next-auth/react";
import { toast } from "sonner";
import { useUser } from "@/context/user-context";
import { useCart } from "@/context/cart-context";
import { createSubscription } from "@/src/services/subscriptionService";
import { TrialActivationOverlay } from "@/components/trial/trial-activation-overlay";
import { usePostLoginSubscription } from "@/src/hooks/usePostLoginSubscription";
import { SubscriptionInfoDialog } from "@/components/trial/subscription-info-dialog";

export default function Home() {
  const [heroData, setHeroData] = useState<{
    title: string;
    subtitle: string;
    description: string;
    buttons: { text: string }[];
    heroImage: { url: string };
  } | null>(null);
  const [benefitsData, setBenefitsData] = useState<{
    id: number;
    title: string;
    description: string;
    cards: { description: string; icon: string; text: string; id: string }[];
  } | null>(null);
  const [featuresData, setFeaturesData] = useState<{
    id: number;
    title: string;
    description: string;
    keyfeatures: { description: string; icon: string; text: string; id: string }[];
  } | null>(null);
  const [servicesData, setServicesData] = useState<{
    id: number;
    title: string;
    description: string;
    servicesCard: { tag: string | null; title: string; decription: string; id: string }[];
  } | null>(null);
  const [faqData, setFaqData] = useState<{
    id: number;
    title: string;
    faqComponent: { id: string; question: string; answer: RichTextNode[] }[];
  } | null>(null);
  const [pricingData, setPricingData] = useState<{
    id: number;
    heading?: string;
    title?: string;
    subscriptionPlan?: {
      monthlyPricing?: number;
      yearlyPricing?: number;
      tag?: string;
      name: string;
      plan_code_monthly?: string;
      plan_code_yearly?: string;
      id: string;
      description?: string;
      button?: { text: string; id: string };
      features?: { feature: string; isIncluded: boolean; id: string }[];
    }[];
    enterprisePlan?: {
      name: string;
      tag?: string;
      description?: string;
      id: string;
      button?: { text: string; id: string };
      features?: { feature: string; isIncluded: boolean; id: string }[];
    }[];
    trialPlan?: {
      name: string;
      tag?: string;
      plan_code: string;
      trialDurationInDays: number;
      features?: { feature: string; isIncluded: boolean; id: string }[];
      description?: string;
      button?: { text: string; id: string };
    };
  } | null>(null);
  const pathname = usePathname();
  const slug = pathname.split("/").pop() || "";

  // Add state for handling post-auth trial activation
  const [isActivatingTrial, setIsActivatingTrial] = useState(false);
  const [activationError, setActivationError] = useState<string | undefined>();
  const searchParams = useSearchParams();
  const { status } = useSession();
  const { userId, userEmail } = useUser();
  const { addItem, clearCart } = useCart();

  // Use the post-login subscription hook to handle subscription processing
  const {
    isSubscriptionInfoOpen,
    setIsSubscriptionInfoOpen,
    subscription
  } = usePostLoginSubscription();

  // Add refs for sections
  const pricingSectionRef = useRef<HTMLDivElement>(null);
  const benefitsSectionRef = useRef<HTMLDivElement>(null);
  const featuresSectionRef = useRef<HTMLDivElement>(null);
  const servicesSectionRef = useRef<HTMLDivElement>(null);
  const faqSectionRef = useRef<HTMLDivElement>(null);
  const contactSectionRef = useRef<HTMLDivElement>(null);
  const testimonialSectionRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const loadHeroContent = async (slug: string) => {
      try {
        const heroSection = await getHeroContentBySlug(slug);
        setHeroData(heroSection);
      } catch (error) {
        console.error("Error loading hero content:", error);
      }
    };

    const loadBenefitsContent = async (slug: string) => {
      try {
        const benefitsSection = await getBenefitsContentBySlug(slug);
        setBenefitsData(benefitsSection);
      } catch (error) {
        console.error("Error loading benefits content:", error);
      }
    };

    const loadFeaturesContent = async (slug: string) => {
      try {
        const featuresSection = await getFeaturesContentBySlug(slug);
        setFeaturesData(featuresSection);
      } catch (error) {
        console.error("Error loading features content:", error);
      }
    };

    const loadServicesContent = async (slug: string) => {
      try {
        const servicesSection = await getServicesContentBySlug(slug);
        setServicesData(servicesSection);
      } catch (error) {
        console.error("Error loading services content:", error);
      }
    };

    const loadFaqContent = async (slug: string) => {
      try {
        const faqSection = await getFaqContentBySlug(slug);
        setFaqData(faqSection);
      } catch (error) {
        console.error("Error loading FAQ content:", error);
      }
    };

    const loadPricingContent = async (slug: string) => {
      try {
        const pricingSection = await getPricingContentBySlug(slug);
        setPricingData(pricingSection);
      } catch (error) {
        console.error("Error loading pricing content:", error);
      }
    };

    if (slug) {
      loadHeroContent(slug);
      loadBenefitsContent(slug);
      loadFeaturesContent(slug);
      loadServicesContent(slug);
      loadFaqContent(slug);
      loadPricingContent(slug);
    }
  }, [slug]);

  // Handle post-authentication trial activation
  useEffect(() => {
    const handleTrialActivation = async () => {
      // Only proceed if the user is authenticated and has trial=true in URL
      if (status !== "authenticated" || !userId || searchParams.get("trial") !== "true") {
        return;
      }

      // Check if we have stored trial data
      const trialPlanCode = localStorage.getItem("trial_plan_code");
      const trialFormDataStr = localStorage.getItem("trial_form_data");
      const trialProductSlug = localStorage.getItem("trial_product_slug");

      // Only proceed if we have the necessary data and it matches current product
      if (!trialPlanCode || !trialFormDataStr || trialProductSlug !== slug) {
        return;
      }

      try {
        setIsActivatingTrial(true);
        setActivationError(undefined);

        // Get trial plan name from pricing data if available
        let trialName = "Trial Subscription";
        let trialDays = 14;

        if (pricingData?.trialPlan) {
          trialName = pricingData.trialPlan.name;
          trialDays = pricingData.trialPlan.trialDurationInDays || 14;
        }

        // Check if user already has an active subscription for this product
        // Extract product code from the trial plan code
        const productCode = trialPlanCode.split('_')[0];
        const url = `${process.env.NEXT_PUBLIC_LAGO_API_URL}/subscriptions?external_customer_id=${encodeURIComponent(userId)}&status[]=active&status[]=pending`;

        console.log(`🔍 PRODUCT PAGE - Checking active subscriptions for user ${userId} and product ${productCode}`);

        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Authorization': `Bearer ${process.env.NEXT_PUBLIC_LAGO_API_KEY}`
          }
        });

        if (response.ok) {
          const data = await response.json();
          const subscriptions = data.subscriptions || [];

          // Filter for subscriptions of this product
          const productSubscriptions = subscriptions.filter((sub: unknown) => {
            const subProductCode = (sub as { plan_code: string }).plan_code.split('_')[0];
            return subProductCode === productCode;
          });

          if (productSubscriptions.length > 0) {
            console.log(`⚠️ PRODUCT PAGE - User already has ${productSubscriptions.length} active/pending subscriptions for product ${productCode}`);

            // Show a toast informing the user they already have a subscription
            toast.info("You already have an active subscription for this product. Redirecting to dashboard.", {
              duration: 5000
            });

            // Clear stored data
            localStorage.removeItem("trial_plan_code");
            localStorage.removeItem("trial_form_data");
            localStorage.removeItem("trial_product_slug");

            // Redirect to dashboard after a short delay
            setTimeout(() => {
              window.location.href = `${process.env.NEXT_PUBLIC_DASHBOARD_URL}/subscriptions`;
            }, 2000);

            return;
          }
        }

        // Create cart item and subscription
        const trialCartItem = {
          id: trialPlanCode,
          name: trialName,
          planCode: trialPlanCode,
          price: 0,
          planDuration: "trial" as "trial" | "monthly" | "yearly",
          trialDays: trialDays,
          quantity: 1,
          image: ""
        };

        // Create subscription without adding to cart
        await createSubscription(
          userId,
          trialCartItem
        );

        // Clear the cart
        if (clearCart) {
          clearCart();
        }

        // Clear stored data
        localStorage.removeItem("trial_plan_code");
        localStorage.removeItem("trial_form_data");
        localStorage.removeItem("trial_product_slug");

        // Show success toast
        toast.success(`Trial activated successfully! Enjoy your ${trialDays}-day trial.`);

        // The redirection will be handled by the TrialActivationOverlay
        // Keep the overlay visible for the countdown
        // Do not redirect immediately
      } catch (error) {
        console.error("Error activating trial after auth:", error);
        const errorMessage = error instanceof Error ? error.message : "Failed to activate trial. Please try again.";
        toast.error(errorMessage);
        setActivationError(errorMessage);
      }
    };

    handleTrialActivation();
  }, [status, userId, userEmail, slug, searchParams, addItem, clearCart, pricingData]);

  // Handle redirection to dashboard
  const handleRedirectToDashboard = () => {
    setIsActivatingTrial(false);
    // Clean URL params
    window.history.replaceState({}, document.title, window.location.pathname);
    // Redirect to dashboard
    window.location.href = `${process.env.NEXT_PUBLIC_DASHBOARD_URL}/subscriptions`;
  };

  // Handle staying on the current page
  const handleStayHere = () => {
    setIsActivatingTrial(false);
    // Clean URL params
    window.history.replaceState({}, document.title, window.location.pathname);
    // Show success toast
    toast.success("Trial activated! You can now explore the site.");
  };

  // Function to scroll to pricing section
  const scrollToPricing = () => {
    if (pricingSectionRef.current) {
      // Smooth scroll to pricing section
      pricingSectionRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });

      // Add visual indication that we've scrolled to this section
      setTimeout(() => {
        // Add a highlight animation to the pricing section
        if (pricingSectionRef.current) {
          pricingSectionRef.current.classList.add('highlight-section');

          // Remove the highlight after animation completes
          setTimeout(() => {
            if (pricingSectionRef.current) {
              pricingSectionRef.current.classList.remove('highlight-section');
            }
          }, 2000);
        }
      }, 500); // Wait for scroll to complete
    }
  };

  // Handle hash-based navigation for deep linking to sections
  useEffect(() => {
    // Function to scroll to a section based on the hash
    const scrollToSection = (hash: string) => {
      // Remove the # from the hash
      const targetId = hash.replace('#', '');

      // Map of section IDs to their refs
      const sectionRefs: Record<string, React.RefObject<HTMLDivElement>> = {
        'pricing-section': pricingSectionRef,
        'benefits-section': benefitsSectionRef,
        'features-section': featuresSectionRef,
        'services-section': servicesSectionRef,
        'faq-section': faqSectionRef,
        'contact-section': contactSectionRef,
        'testimonial-section': testimonialSectionRef
      };

      // Check if the hash starts with any of our section prefixes
      const sectionPrefix = Object.keys(sectionRefs).find(prefix =>
        targetId.startsWith(prefix)
      );

      if (sectionPrefix && sectionRefs[sectionPrefix]?.current) {
        // Scroll to the section
        sectionRefs[sectionPrefix].current?.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });

        // Add highlight effect
        setTimeout(() => {
          if (sectionRefs[sectionPrefix].current) {
            sectionRefs[sectionPrefix].current?.classList.add('highlight-section');

            // Remove highlight after animation completes
            setTimeout(() => {
              if (sectionRefs[sectionPrefix].current) {
                sectionRefs[sectionPrefix].current?.classList.remove('highlight-section');
              }
            }, 2000);
          }
        }, 500);
      }
    };

    // Check for hash on initial load
    if (window.location.hash) {
      // Delay to ensure all content is loaded
      setTimeout(() => {
        scrollToSection(window.location.hash);
      }, 500);
    }

    // Listen for hash changes
    const handleHashChange = () => {
      if (window.location.hash) {
        scrollToSection(window.location.hash);
      }
    };

    window.addEventListener('hashchange', handleHashChange);

    // Expose the scrollToPricing function to the window object
    if (typeof window !== 'undefined') {
      // Use index signature to safely add the function
      (window as Window & { scrollToPricing?: () => void }).scrollToPricing = scrollToPricing;
    }

    // Cleanup function
    return () => {
      window.removeEventListener('hashchange', handleHashChange);

      if (typeof window !== 'undefined' && 'scrollToPricing' in window) {
        // Use delete with optional chaining
        delete (window as Window & { scrollToPricing?: () => void }).scrollToPricing;
      }
    };
  }, []);

  return (
    <>
      <TrialActivationOverlay
        isVisible={isActivatingTrial}
        onRedirect={handleRedirectToDashboard}
        onStayHere={handleStayHere}
        redirectTime={5}
        error={activationError}
      />

      {heroData && (
        <EnhancedHeroSection
          title={heroData.title}
          description={heroData.description}
          buttons={heroData.buttons}
          heroImage={heroData.heroImage}
          productSlug={slug}
          productName={heroData.title}
          trialPlan={pricingData?.trialPlan}
          onScrollToPricing={scrollToPricing}
          onScrollToDemo={() => {
            const demoSection = document.getElementById('demo');
            if (demoSection) {
              demoSection.scrollIntoView({ behavior: 'smooth' });
            }
          }}
          category={heroData.category || "SaaS Solution"}
        />
      )}


      {benefitsData && (
        <BenefitsSection
          title={benefitsData?.title}
          description={benefitsData?.description}
          cards={benefitsData?.cards}
          ref={benefitsSectionRef}
          sectionComponent="sections.benefits-section"
          sectionId={benefitsData.id}
        />
      )}
      {featuresData && (
        <FeaturesSection
          title={featuresData?.title}
          description={featuresData?.description}
          keyfeatures={featuresData?.keyfeatures}
          ref={featuresSectionRef}
          sectionComponent="sections.key-feature-section"
          sectionId={featuresData.id}
        />
      )}
      {servicesData && (
        <ServicesSection
          title={servicesData?.title}
          description={servicesData?.description}
          servicesCard={servicesData?.servicesCard}
          ref={servicesSectionRef}
          sectionComponent="sections.services-section"
          sectionId={servicesData.id}
        />
      )}

      {/* <SponsorsSection /> */}
      <div ref={testimonialSectionRef}>
        <TestimonialSection
          slug={slug}
          sectionComponent="sections.testimonial-section"
          sectionId={1} // This would ideally come from the API
        />
      </div>
      {pricingData && (
        <div ref={pricingSectionRef}>
          <PricingSection
            slug={slug}
            sectionComponent="sections.pricing-section"
            sectionId={pricingData.id || 1}
          />
        </div>
      )}
      <div ref={contactSectionRef}>
        <ContactSection
          sectionComponent="sections.contact-section"
          sectionId={1} // This would ideally come from the API
        />
      </div>
      <NewsletterSection />
      {/* <CommunitySection /> */}
      {faqData && (
        <FAQSection
          title={faqData?.title}
          faqComponent={faqData?.faqComponent}
          ref={faqSectionRef}
          sectionComponent="sections.faq-section"
          sectionId={faqData.id}
        />
      )}
      <FooterSection />

      {/* Subscription Info Dialog */}
      {subscription && (
        <SubscriptionInfoDialog
          open={isSubscriptionInfoOpen}
          onClose={() => setIsSubscriptionInfoOpen(false)}
          productName={heroData?.title || slug}
          productSlug={slug}
          currentPlan={subscription}
          onViewPricing={() => {
            setIsSubscriptionInfoOpen(false);
            // Scroll to pricing section
            if (pricingSectionRef.current) {
              pricingSectionRef.current.scrollIntoView({ behavior: 'smooth' });
            }
          }}
          onGoToDashboard={() => window.location.href = `${process.env.NEXT_PUBLIC_DASHBOARD_URL}/subscriptions`}
        />
      )}
    </>
  );
}
