import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// Lead form validation schema
const leadSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email address"),
  phone: z.string().min(10, "Please enter a valid phone number"),
  organization: z.string().min(2, "Organization name is required"),
  organizationType: z.enum(["startup", "small-business", "enterprise", "non-profit", "other"]),
  projectType: z.enum(["web-app", "mobile-app", "desktop-app", "api-integration", "automation", "other"]),
  budget: z.enum(["under-10k", "10k-25k", "25k-50k", "50k-100k", "over-100k"]),
  timeline: z.enum(["asap", "1-3-months", "3-6-months", "6-12-months", "flexible"]),
  brief: z.string().min(50, "Please provide at least 50 characters describing your project"),
  requirements: z.string().optional(),
  source: z.string().optional(),
  timestamp: z.string().optional(),
});

type LeadData = z.infer<typeof leadSchema>;

// Helper function to send notification email (placeholder)
async function sendLeadNotification(leadData: LeadData) {
  // In a real implementation, you would integrate with your email service
  // For example: SendGrid, Mailgun, AWS SES, etc.
  
  console.log('📧 New lead notification would be sent:', {
    to: process.env.LEADS_EMAIL || '<EMAIL>',
    subject: `New Custom Development Lead: ${leadData.name} from ${leadData.organization}`,
    leadData
  });

  // Placeholder for email service integration
  // await emailService.send({
  //   to: process.env.LEADS_EMAIL,
  //   template: 'new-lead',
  //   data: leadData
  // });
}

// Helper function to save lead to database (placeholder)
async function saveLeadToDatabase(leadData: LeadData) {
  // In a real implementation, you would save to your database
  // For example: PostgreSQL, MongoDB, Supabase, etc.
  
  console.log('💾 Lead would be saved to database:', leadData);

  // Placeholder for database integration
  // const lead = await db.leads.create({
  //   data: {
  //     ...leadData,
  //     status: 'new',
  //     createdAt: new Date(),
  //   }
  // });
  
  // return lead;
  
  return {
    id: `lead_${Date.now()}`,
    ...leadData,
    status: 'new',
    createdAt: new Date().toISOString(),
  };
}

// Helper function to integrate with CRM (placeholder)
async function syncToCRM(leadData: LeadData) {
  // In a real implementation, you would sync to your CRM
  // For example: HubSpot, Salesforce, Pipedrive, etc.
  
  console.log('🔄 Lead would be synced to CRM:', leadData);

  // Placeholder for CRM integration
  // await crmService.createContact({
  //   firstName: leadData.name.split(' ')[0],
  //   lastName: leadData.name.split(' ').slice(1).join(' '),
  //   email: leadData.email,
  //   phone: leadData.phone,
  //   company: leadData.organization,
  //   customFields: {
  //     projectType: leadData.projectType,
  //     budget: leadData.budget,
  //     timeline: leadData.timeline,
  //     brief: leadData.brief,
  //   }
  // });
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate the request body
    const validatedData = leadSchema.parse(body);
    
    // Log the lead submission for debugging
    console.log('📝 New custom development lead received:', {
      name: validatedData.name,
      organization: validatedData.organization,
      projectType: validatedData.projectType,
      budget: validatedData.budget,
      source: validatedData.source,
      timestamp: validatedData.timestamp,
    });

    // Process the lead in parallel
    const [savedLead] = await Promise.allSettled([
      saveLeadToDatabase(validatedData),
      sendLeadNotification(validatedData),
      syncToCRM(validatedData),
    ]);

    // Check if the main operation (saving to database) was successful
    if (savedLead.status === 'rejected') {
      console.error('Failed to save lead to database:', savedLead.reason);
      throw new Error('Failed to process lead submission');
    }

    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Lead submitted successfully',
      leadId: savedLead.value.id,
      nextSteps: {
        calendlyUrl: process.env.CALENDLY_URL || 'https://calendly.com/your-calendly-link',
        responseTime: '24 hours',
        contactEmail: process.env.LEADS_EMAIL || '<EMAIL>',
      }
    }, { status: 201 });

  } catch (error) {
    console.error('Error processing lead submission:', error);
    
    // Handle validation errors
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        message: 'Invalid form data',
        errors: error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message,
        })),
      }, { status: 400 });
    }

    // Handle other errors
    return NextResponse.json({
      success: false,
      message: 'Internal server error. Please try again or contact support.',
      error: process.env.NODE_ENV === 'development' ? (error instanceof Error ? error.message : String(error)) : undefined,
    }, { status: 500 });
  }
}

// Handle GET requests (optional - for testing)
export async function GET() {
  return NextResponse.json({
    message: 'Custom Development Leads API',
    endpoints: {
      POST: '/api/leads - Submit a new lead',
    },
    version: '1.0.0',
  });
}
