"use client";
import React from "react";
import { motion, AnimatePresence } from "framer-motion";
import Link from "next/link";
import Image from "next/image";
import { cn } from "@/lib/utils";
import { MegaMenu } from "./mega-menu";
import type { ProductResponse } from "../../@types/product";



// Enhanced MegaMenuItem for Products dropdown with mega menu layout
export const MegaMenuItem = ({
  setActive,
  active,
  item,
  products,
  loading,
}: {
  setActive: (item: string | null) => void;
  active: string | null;
  item: string;
  products: ProductResponse[];
  loading: boolean;
}) => {
  const [isHovered, setIsHovered] = React.useState(false);
  const timeoutRef = React.useRef<NodeJS.Timeout>();

  React.useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const handleMouseEnter = React.useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setIsHovered(true);
    setActive(item);
  }, [item, setActive]);

  const handleMouseLeave = React.useCallback(() => {
    timeoutRef.current = setTimeout(() => {
      setIsHovered(false);
      setActive(null);
    }, 150); // Slightly longer delay for mega menu
  }, [setActive]);

  const handleClose = React.useCallback(() => {
    setIsHovered(false);
    setActive(null);
  }, [setActive]);

  return (
    <div
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      className="relative"
    >
      <motion.p
        transition={{ duration: 0.2 }}
        className="cursor-pointer text-black hover:opacity-[0.9] dark:text-white"
      >
        {item}
      </motion.p>
      <AnimatePresence>
        {(active === item || isHovered) && (
          <motion.div
            initial={{ opacity: 0, scale: 0.98, y: 10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.98, y: 10 }}
            transition={{
              duration: 0.3,
              ease: [0.4, 0, 0.2, 1]
            }}
            className="absolute top-[calc(100%_+_0.75rem)] left-1/2 transform -translate-x-1/2 pt-2 z-50"
          >
            <motion.div
              transition={{ duration: 0.3 }}
              className="bg-white dark:bg-black backdrop-blur-sm rounded-2xl overflow-hidden border border-black/[0.2] dark:border-white/[0.2] shadow-2xl"
            >
              <MegaMenu
                products={products}
                loading={loading}
                isOpen={active === item || isHovered}
                onClose={handleClose}
              />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

// Common MenuItem component to be used in both navbars
export const MenuItem = ({
  setActive,
  active,
  item,
  children,
}: {
  setActive: (item: string | null) => void;
  active: string | null;
  item: string;
  children?: React.ReactNode;
}) => {
  const [isHovered, setIsHovered] = React.useState(false);
  const timeoutRef = React.useRef<NodeJS.Timeout>();

  React.useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const handleMouseEnter = React.useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setIsHovered(true);
    setActive(item);
  }, [item, setActive]);

  const handleMouseLeave = React.useCallback(() => {
    timeoutRef.current = setTimeout(() => {
      setIsHovered(false);
      setActive(null);
    }, 100); // Small delay to prevent flickering
  }, [setActive]);

  return (
    <div
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      className="relative"
    >
      <motion.p
        transition={{ duration: 0.2 }}
        className="cursor-pointer text-black hover:opacity-[0.9] dark:text-white"
      >
        {item}
      </motion.p>
      <AnimatePresence>
        {(active === item || isHovered) && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 10 }}
            transition={{
              duration: 0.2,
              ease: [0.4, 0, 0.2, 1]
            }}
            onAnimationComplete={() => {
              if (!isHovered && active !== item) {
                setActive(null);
              }
            }}
          >
            <div className="absolute top-[calc(100%_+_0.75rem)] left-1/2 transform -translate-x-1/2 pt-2">
              <motion.div
                transition={{ duration: 0.2 }}
                className="bg-white dark:bg-black backdrop-blur-sm rounded-xl overflow-hidden border border-black/[0.2] dark:border-white/[0.2] shadow-lg"
              >
                <motion.div
                  className="w-max max-h-[350px] overflow-y-auto h-full p-2"
                  style={{
                    scrollbarWidth: 'thin',
                    scrollbarColor: 'rgba(156, 163, 175, 0.5) transparent',
                  }}
                >
                  {children}
                </motion.div>
              </motion.div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

// Common Menu component to be used in both navbars
export const Menu = ({
  setActive,
  children,
  className,
}: {
  setActive: (item: string | null) => void;
  children: React.ReactNode;
  className?: string;
}) => {
  return (
    <nav
      onMouseLeave={() => setActive(null)}
      className={cn(
        "relative rounded-full border border-transparent dark:bg-black dark:border-white/[0.2] bg-white shadow-input flex justify-center space-x-4 px-8 py-3",
        className
      )}
    >
      {children}
    </nav>
  );
};

// Common ProductItem component to be used in both navbars
export const ProductItem = ({
  title,
  description,
  href,
  src,
}: {
  title: string;
  description: string;
  href: string;
  src: string;
}) => {
  return (
    <Link href={href} className="flex space-x-3 items-center p-1.5 hover:bg-slate-100 dark:hover:bg-slate-800 rounded-lg transition-all duration-200">
      <div className="flex-shrink-0 h-[50px] w-[50px] rounded-md overflow-hidden bg-gradient-to-br from-red-50 via-pink-50 to-rose-100 dark:from-slate-800 dark:via-slate-700 dark:to-slate-800">
        <Image
          src={src}
          width={50}
          height={50}
          alt={title}
          className="h-full w-full object-contain p-1"
          onError={(e) => {
            e.currentTarget.src = "/placeholder.jpg";
          }}
        />
      </div>
      <div className="flex-1 min-w-0">
        <h4 className="text-sm font-semibold mb-0.5 truncate text-foreground">
          {title}
        </h4>
        <p className="text-muted-foreground text-xs line-clamp-2 max-w-[180px]">
          {description}
        </p>
      </div>
    </Link>
  );
};

// Common HoveredLink component to be used in both navbars
export const HoveredLink = ({ children, ...rest }: React.ComponentProps<typeof Link>) => {
  return (
    <Link
      {...rest}
      className="text-neutral-700 dark:text-neutral-200 hover:text-black "
    >
      {children}
    </Link>
  );
};
