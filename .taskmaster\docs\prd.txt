﻿PRD: SaaS Portal Landing & Navigation Revamp
Project Objective:
 Deliver a modern, high-conversion landing experience for a SaaS marketplace (Odoo/Zoho–style) that:
* Instantly communicates offerings across four verticals (Domestic, Commercial, Production/Creative, Custom Solutions)

* Funnels users toward product discovery, pricing, and signup

* Uses top-tier UI/UX components and best animation libraries

________________


Core Features & Tasks
   * Build hero section:

      * Includes value proposition, animated ecosystem snapshot, CTAs (“Explore Products” / “Start Free Trial”)

      * Use Hero UI + Framer Motion animation

         * Create category card section:

            * Four cards: Domestic, Commercial, Production/Creative, Custom Development

            * Entire card is clickable; visual separation, vibrant icons

            * Implement with Magic Design UI; use GSAP for hover/entrance effects

               * Implement product grid components:

                  * For each category, show 3 featured products

                  * Fetch from Strapi endpoint /api/products?populate[sections][populate]=*

                  * New <ProductGrid category="X" /> React component; fully responsive

                     * Add command palette search (⌘K):

                        * Use shadcn/ui Combobox or MagicUI Search

                        * Must find products, documentation, and pricing instantly

                           * Design and integrate sticky top navigation bar:

                              * Logo, Products dropdown (grouped by category), Pricing, Solutions, Support, Sign In/Start Free Trial

                              * Category dropdown uses Aceternity UI or Hero UI mega-menu

                                 * Develop social proof/testimonial slider:

                                    * Incorporate top brand logos and customer testimonials

                                    * Cult UI carousel, with Framer Motion entrance/fade animations

                                       * Implement “How subscription works” section:

                                          * Three-step graphic (Choose product → Select plan → Start for free)

                                          * React Bits UI Steps + GSAP line draw

                                             * Add custom development lead form:

                                                * Multi-step modal (shadcn/ui Dialog or Hero UI)

                                                * Collect name, org, contact, brief

                                                * POST to /api/leads, show Calendly scheduling on success

                                                   * Build SEO-friendly footer:

                                                      * Sitemap, legal, language switcher (next-intl ready), social links

________________


Product & Pricing Navigation
                                                         * Route /products/[slug] for product landing pages:

                                                            * Product hero, screenshots, feature tabs, demo CTA

                                                               * Route /products/[slug]/pricing:

                                                                  * Pricing tiers table with monthly/yearly toggle

                                                                  * Integrate PlanSelector; Stripe Checkout links

                                                                     * Ensure ISR (Incremental Static Regeneration) on all product pages (revalidate: 5min)

                                                                     * Limit user trial: 14-day trial per account only (enforced server-side)

                                                                     * Surface “Contact Sales” instead of in-app plan change/upgrade/downgrade

________________


Animation, Performance, Accessibility
                                                                        * All interactive sections use Framer Motion for entry/presence

                                                                        * Large/scroll-based effects (category grid, steps) use GSAP ScrollTrigger

                                                                        * All motion/animation must respect prefers-reduced-motion accessibility

                                                                        * Page must be WCAG 2.2 AA compliant

                                                                        * Landing bundle: ≤ 250 kB gzipped before hydration

________________


Platform & Stack
                                                                           * Next.js 15 App Router, TypeScript, Turbopack

                                                                           * UI: shadcn/ui (core), Aceternity UI, Hero UI, Magic Design UI, React Bits UI, Cult UI

                                                                           * Animation: Framer Motion, GSAP

                                                                           * Data: Strapi (existing endpoints for products/sections)

                                                                           * Payments: Stripe Billing

                                                                           * Analytics: Segment, PostHog

                                                                           * i18n: next-intl (en default, ready for hi/es)

                                                                           * Automated testing: Playwright e2e for main flows

                                                                           * Lighthouse CI: LCP < 2.5s, FCP < 1.8s, Perf/Acc/SEO/Best ≥ 90

________________


Milestones / Timeline
                                                                              * Library integration & theme tokens: 1 week

                                                                              * Landing skeleton, nav, search: 1 week

                                                                              * Product pages, pricing, checkout: 1 week

                                                                              * Custom dev lead flow, testimonials, a11y: 1 week

                                                                              * QA, Lighthouse, prelaunch: 1 week

________________


Acceptance Criteria
                                                                                 * Lighthouse: Performance, Accessibility, SEO, Best Practices ≥ 90

                                                                                 * Product selection to plan selection in <6s for new users

                                                                                 * Command palette returns results in <150ms

                                                                                 * Social proof and testimonial slider visible on first scroll

                                                                                 * Stripe test checkout and lead form POST both work in staging

________________


Out of Scope
                                                                                    * In-app subscription upgrade/downgrade (handled by sales only)

                                                                                    * Redesign of admin dashboard

                                                                                    * Multi-tenant management UI

________________


Instructions for Claude Task Master:
                                                                                       * Save as .txt file and run task-master parse-prd ...

                                                                                       * Each major bullet/section becomes a separate actionable task

                                                                                       * Review and break down large bullets if needed after parsing

________________