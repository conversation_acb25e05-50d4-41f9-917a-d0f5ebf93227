"use client";

import { useEffect, useCallback, useRef } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { 
  cinematicZoomTransition,
  cinematicCardHover,
  preloadOnHover,
  initializeCinematicTransitions,
  cleanupCinematicTransitions
} from '@/lib/cinematic-transitions';

interface UseCinematicTransitionsOptions {
  enableCinematicMode?: boolean;
  enablePreloading?: boolean;
  enableHoverEffects?: boolean;
}

export function useCinematicTransitions({
  enableCinematicMode = true,
  enablePreloading = true,
  enableHoverEffects = true
}: UseCinematicTransitionsOptions = {}) {
  const router = useRouter();
  const pathname = usePathname();
  const isTransitioningRef = useRef(false);
  const preloadCleanupRef = useRef<Map<HTMLElement, () => void>>(new Map());

  // Initialize cinematic transitions on mount
  useEffect(() => {
    if (enableCinematicMode) {
      initializeCinematicTransitions();
    }

    return () => {
      if (enableCinematicMode) {
        cleanupCinematicTransitions();
      }
    };
  }, [enableCinematicMode]);

  // Cleanup preload listeners on unmount
  useEffect(() => {
    return () => {
      preloadCleanupRef.current.forEach(cleanup => cleanup());
      preloadCleanupRef.current.clear();
    };
  }, []);

  // Reset transition state on route change
  useEffect(() => {
    isTransitioningRef.current = false;
  }, [pathname]);

  // Enhanced product card click with cinematic transition
  const handleCinematicCardClick = useCallback(async (
    event: React.MouseEvent<HTMLElement>,
    productSlug: string,
    productName?: string,
    options?: {
      section?: string;
      enableCinematic?: boolean;
    }
  ) => {
    if (!enableCinematicMode || isTransitioningRef.current) {
      // Fallback to regular navigation
      const url = options?.section 
        ? `/products/${productSlug}#${options.section}`
        : `/products/${productSlug}`;
      window.location.href = url;
      return;
    }

    event.preventDefault();
    event.stopPropagation();

    try {
      isTransitioningRef.current = true;
      
      const cardElement = event.currentTarget;
      const targetUrl = options?.section 
        ? `/products/${productSlug}#${options.section}`
        : `/products/${productSlug}`;

      // Use cinematic transition if enabled for this click
      if (options?.enableCinematic !== false) {
        await cinematicZoomTransition(cardElement, targetUrl, productName);
      } else {
        // Fallback to regular navigation
        window.location.href = targetUrl;
      }
    } catch (error) {
      console.error('Cinematic transition failed:', error);
      isTransitioningRef.current = false;
      
      // Fallback navigation
      const url = options?.section 
        ? `/products/${productSlug}#${options.section}`
        : `/products/${productSlug}`;
      window.location.href = url;
    }
  }, [enableCinematicMode]);

  // Enhanced card hover with cinematic effects
  const handleCinematicCardHover = useCallback((
    element: HTMLElement,
    isHovering: boolean,
    targetUrl?: string
  ) => {
    if (!enableHoverEffects) return;

    cinematicCardHover(element, isHovering);

    // Setup preloading on hover
    if (isHovering && targetUrl && enablePreloading) {
      const cleanup = preloadOnHover(element, targetUrl);
      preloadCleanupRef.current.set(element, cleanup);
    } else if (!isHovering && preloadCleanupRef.current.has(element)) {
      const cleanup = preloadCleanupRef.current.get(element);
      if (cleanup) {
        cleanup();
        preloadCleanupRef.current.delete(element);
      }
    }
  }, [enableHoverEffects, enablePreloading]);

  // Auto-attach cinematic effects to product cards
  useEffect(() => {
    if (!enableCinematicMode && !enableHoverEffects) return;

    const attachCinematicEffects = () => {
      const productCards = document.querySelectorAll(
        '[data-product-card], [class*="product-card"], .product-item'
      );
      
      productCards.forEach((card) => {
        const element = card as HTMLElement;
        
        // Skip if already processed
        if (element.dataset.cinematicAttached) return;
        element.dataset.cinematicAttached = 'true';
        
        // Extract product info from card
        const productSlug = element.dataset.productSlug || 
          element.querySelector('[data-product-slug]')?.getAttribute('data-product-slug');
        const productName = element.dataset.productName ||
          element.querySelector('h3, h2, [class*="title"]')?.textContent;
        
        if (!productSlug) return;
        
        const targetUrl = `/products/${productSlug}`;
        
        // Add hover effects
        const handleMouseEnter = () => {
          handleCinematicCardHover(element, true, targetUrl);
        };
        
        const handleMouseLeave = () => {
          handleCinematicCardHover(element, false, targetUrl);
        };
        
        // Add click handler for cinematic transition
        const handleClick = (e: Event) => {
          if (enableCinematicMode) {
            handleCinematicCardClick(
              e as any,
              productSlug,
              productName || undefined,
              { enableCinematic: true }
            );
          }
        };
        
        element.addEventListener('mouseenter', handleMouseEnter);
        element.addEventListener('mouseleave', handleMouseLeave);
        element.addEventListener('click', handleClick);
        
        // Store cleanup function
        const cleanup = () => {
          element.removeEventListener('mouseenter', handleMouseEnter);
          element.removeEventListener('mouseleave', handleMouseLeave);
          element.removeEventListener('click', handleClick);
          delete element.dataset.cinematicAttached;
        };
        
        preloadCleanupRef.current.set(element, cleanup);
      });
    };

    // Initial attachment
    attachCinematicEffects();

    // Re-attach when DOM changes
    const observer = new MutationObserver((mutations) => {
      let shouldReattach = false;
      
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as HTMLElement;
              if (element.matches && element.matches('[data-product-card], [class*="product-card"], .product-item')) {
                shouldReattach = true;
              }
            }
          });
        }
      });
      
      if (shouldReattach) {
        setTimeout(attachCinematicEffects, 100);
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });

    return () => {
      observer.disconnect();
    };
  }, [enableCinematicMode, enableHoverEffects, handleCinematicCardHover, handleCinematicCardClick]);

  // Utility function to create product slug from name
  const createProductSlug = useCallback((productName: string): string => {
    return productName
      .toLowerCase()
      .replace(/[^\w\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .trim();
  }, []);

  // Enhanced link props for cinematic navigation
  const getCinematicLinkProps = useCallback((
    productSlug: string,
    productName?: string,
    options?: {
      section?: string;
      enableCinematic?: boolean;
    }
  ) => {
    const href = options?.section 
      ? `/products/${productSlug}#${options.section}`
      : `/products/${productSlug}`;
    
    return {
      href,
      'data-product-slug': productSlug,
      'data-product-name': productName,
      'data-product-card': 'true',
      onClick: (e: React.MouseEvent<HTMLAnchorElement>) => {
        if (enableCinematicMode && options?.enableCinematic !== false) {
          e.preventDefault();
          handleCinematicCardClick(
            e as any,
            productSlug,
            productName,
            options
          );
        }
      },
    };
  }, [enableCinematicMode, handleCinematicCardClick]);

  // Button props for cinematic transitions
  const getCinematicButtonProps = useCallback((
    productSlug: string,
    productName?: string,
    options?: {
      section?: string;
      enableCinematic?: boolean;
    }
  ) => {
    return {
      'data-product-slug': productSlug,
      'data-product-name': productName,
      onClick: (e: React.MouseEvent<HTMLButtonElement>) => {
        handleCinematicCardClick(
          e as any,
          productSlug,
          productName,
          options
        );
      },
    };
  }, [handleCinematicCardClick]);

  return {
    // Main functions
    handleCinematicCardClick,
    handleCinematicCardHover,
    
    // Utilities
    createProductSlug,
    getCinematicLinkProps,
    getCinematicButtonProps,
    
    // State
    isTransitioning: isTransitioningRef.current,
    
    // Configuration
    enableCinematicMode,
    enablePreloading,
    enableHoverEffects,
  };
}
