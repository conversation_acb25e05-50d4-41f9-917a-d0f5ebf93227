// import { BenefitsSection } from "@/components/layout/sections/benefits";
// import { CommunitySection } from "@/components/layout/sections/community";
import { ContactSection } from "@/components/layout/sections/contact";
import { FAQSection } from "@/components/layout/sections/faq";
// import { FeaturesSection } from "@/components/layout/sections/features";
import { FooterSection } from "@/components/layout/sections/footer";
import { HeroSection } from "@/components/layout/sections/hero";
import { NewsletterSection } from "@/components/layout/sections/newsletter";
// import { PricingSection } from "@/components/layout/sections/pricing";
import { SponsorsSection } from "@/components/layout/sections/sponsors";
// import { TeamSection } from "@/components/layout/sections/team";
// import { TestimonialSection } from "@/components/layout/sections/testimonial";
import { ProductsSection } from "@/components/layout/sections/products";
import { ExploreCategoriesSection } from "@/components/layout/sections/explore-categories";
import { EnhancedSocialProofSection } from "@/components/layout/sections/enhanced-social-proof";
import { SubscriptionWorkflowSection } from "@/components/layout/sections/subscription-workflow";
import BentoGridThirdDemo from "@/components/bento-grid-demo-3";
import InfiniteMovingCardsDemo from "@/components/infinite-moving-cards-demo";

export const metadata = {
  title: `onebiz - SaaS Marketplace `,
  description:
    "A modern, responsive landing page template for developers, optimized for SaaS projects. Built with Shadcn, Tailwind CSS, and Next.js.",
  openGraph: {
    type: "website",
    url: "/",
    title: "onebiz - SaaS Marketplace",
    description: "One Stop Marketplace for all your SaaS needs",
    images: [
      {
        url: "/seo.jpg",
        width: 1200,
        height: 630,
        alt: "onebiz - SaaS Marketplace",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    site: "/",
    title: "onebiz - SaaS Marketplace",
    description: "One Stop Marketplace for all your SaaS needs",
    images: ["/seo.jpg"],
  },
};

export default function Home() {
  return (
    <>
      <HeroSection />
      <ExploreCategoriesSection />
      <SponsorsSection />
      <ProductsSection />
      <div className="container py-12">
        <h2 className="text-3xl font-bold text-center mb-8">AI-Powered Features</h2>
        <BentoGridThirdDemo />
      </div>
      {/* <BenefitsSection /> */}
      {/* <FeaturesSection /> */}
      {/* <ServicesSection /> */}
      <EnhancedSocialProofSection />
      <SubscriptionWorkflowSection />

      {/* <TeamSection /> */}
      {/* <PricingSection /> */}
      {/* <CommunitySection /> */}
      <ContactSection />
      <FAQSection />
      <NewsletterSection />
      <FooterSection />
    </>
  );
}
