// import { BenefitsSection } from "@/components/layout/sections/benefits";
// import { CommunitySection } from "@/components/layout/sections/community";
import { CTASection } from "@/components/layout/sections/cta";
import { FAQSection } from "@/components/layout/sections/faq";
// import { FeaturesSection } from "@/components/layout/sections/features";
import { FooterSection } from "@/components/layout/sections/footer";
import { HeroSection } from "@/components/layout/sections/hero";
import { NewsletterSection } from "@/components/layout/sections/newsletter";
// import { PricingSection } from "@/components/layout/sections/pricing";
import { SponsorsSection } from "@/components/layout/sections/sponsors";
// import { TeamSection } from "@/components/layout/sections/team";
// import { TestimonialSection } from "@/components/layout/sections/testimonial";
import { ProductsSection } from "@/components/layout/sections/products";
import { ExploreCategoriesSection } from "@/components/layout/sections/explore-categories";
import { TestimonialsColumnsSection } from "@/components/layout/sections/testimonials-columns";
import { FeatureStepsSection } from "@/components/layout/sections/feature-steps";
import { AdvancedFeaturesSection } from "@/components/layout/sections/advanced-features";


export const metadata = {
  title: `onebiz - SaaS Marketplace `,
  description:
    "A modern, responsive landing page template for developers, optimized for SaaS projects. Built with Shadcn, Tailwind CSS, and Next.js.",
  openGraph: {
    type: "website",
    url: "/",
    title: "onebiz - SaaS Marketplace",
    description: "One Stop Marketplace for all your SaaS needs",
    images: [
      {
        url: "/seo.jpg",
        width: 1200,
        height: 630,
        alt: "onebiz - SaaS Marketplace",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    site: "/",
    title: "onebiz - SaaS Marketplace",
    description: "One Stop Marketplace for all your SaaS needs",
    images: ["/seo.jpg"],
  },
};

export default function Home() {
  return (
    <>
      <HeroSection />
      <ExploreCategoriesSection />
      <SponsorsSection />
      {/* <ProductsSection /> */}
      <AdvancedFeaturesSection />
      {/* <BenefitsSection /> */}
      {/* <FeaturesSection /> */}
      {/* <ServicesSection /> */}
      <TestimonialsColumnsSection />
      <FeatureStepsSection />

      {/* <TeamSection /> */}
      {/* <PricingSection /> */}
      {/* <CommunitySection /> */}
      <CTASection />
      <FAQSection />
      {/* <NewsletterSection /> */}
      <FooterSection />
    </>
  );
}
