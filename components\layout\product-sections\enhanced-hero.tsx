"use client";

import React, { useState, useEffect } from "react";
import { motion, useInView } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { BackgroundBeamsWithCollision } from "@/components/ui/extras/background-beams-with-collision";
import { 
  ArrowRight, 
  Play, 
  Star, 
  Users, 
  Award, 
  CheckCircle,
  Download,
  ExternalLink,
  Sparkles
} from "lucide-react";
import Image from "next/image";
import { TrialButton } from "@/components/trial/trial-button";
import { cn } from "@/lib/utils";

type EnhancedHeroSectionProps = {
  title?: string;
  subtitle?: string;
  description?: string;
  buttons?: Array<{ text: string }> | { text: string };
  heroImage?: { url: string };
  productSlug?: string;
  productName?: string;
  trialPlan?: {
    name: string;
    tag?: string;
    plan_code: string;
    trialDurationInDays: number;
    features?: { feature: string; isIncluded: boolean; id: string }[];
    description?: string;
    button?: { text: string; id?: string };
  };
  onScrollToPricing?: () => void;
  onScrollToDemo?: () => void;
  category?: string;
  rating?: number;
  userCount?: string;
  awards?: string[];
};

// Trust indicators data
const trustIndicators = [
  { icon: Users, label: "Active Users", value: "50,000+" },
  { icon: Star, label: "Rating", value: "4.9/5" },
  { icon: Award, label: "Awards", value: "12+" },
];

export const EnhancedHeroSection = ({
  title,
  subtitle,
  description,
  buttons = [],
  heroImage,
  productSlug,
  productName,
  trialPlan,
  onScrollToPricing,
  onScrollToDemo,
  category = "SaaS Solution",
  rating = 4.9,
  userCount = "50,000+",
  awards = ["Best SaaS 2024", "Innovation Award"]
}: EnhancedHeroSectionProps) => {
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);
  const heroRef = React.useRef<HTMLDivElement>(null);
  const isInView = useInView(heroRef, { once: true });

  // Normalize buttons to always be an array
  const normalizedButtons = Array.isArray(buttons) ? buttons : [buttons];

  // Find the trial button by text
  const trialButtonIndex = normalizedButtons.findIndex(button => 
    button.text?.toLowerCase().includes("trial") || 
    button.text?.toLowerCase().includes("free") ||
    button.text?.toLowerCase().includes("start") ||
    button.text?.toLowerCase().includes("get started") ||
    button.text?.toLowerCase().includes("try")
  );

  const handleDemoClick = () => {
    if (onScrollToDemo) {
      onScrollToDemo();
    } else {
      // Fallback to demo section or external demo
      const demoSection = document.getElementById('demo');
      if (demoSection) {
        demoSection.scrollIntoView({ behavior: 'smooth' });
      }
    }
  };

  const renderActionButtons = () => {
    return (
      <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
        {/* Primary CTA - Trial Button */}
        {trialPlan ? (
          <TrialButton
            buttonText={trialPlan.button?.text || "Start Free Trial"}
            productSlug={productSlug || ""}
            productName={productName || title || ""}
            trialPlan={trialPlan}
            className="group px-8 py-3 text-lg font-semibold"
            onScrollToPricing={onScrollToPricing}
          />
        ) : (
          <Button size="lg" className="group px-8 py-3 text-lg font-semibold">
            Get Started Free
            <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
          </Button>
        )}

        {/* Secondary CTA - Demo Button */}
        <Button 
          variant="outline" 
          size="lg" 
          onClick={handleDemoClick}
          className="group px-8 py-3 text-lg font-semibold"
        >
          <Play className="mr-2 w-5 h-5 group-hover:scale-110 transition-transform" />
          Watch Demo
        </Button>

        {/* Tertiary CTA - Download/Learn More */}
        <Button 
          variant="ghost" 
          size="lg"
          className="group px-6 py-3 text-base"
        >
          <Download className="mr-2 w-4 h-4" />
          Download Brochure
          <ExternalLink className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" />
        </Button>
      </div>
    );
  };

  return (
    <section ref={heroRef} className="relative overflow-hidden">
      <div className="container mx-auto px-4 py-16 lg:py-24">
        <BackgroundBeamsWithCollision>
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Column - Content */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -50 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="space-y-8"
            >
              {/* Category Badge */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                transition={{ duration: 0.6, delay: 0.3 }}
              >
                <Badge variant="outline" className="px-4 py-2 text-sm font-medium">
                  <Sparkles className="w-4 h-4 mr-2 text-primary" />
                  {category}
                </Badge>
              </motion.div>

              {/* Title */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                className="space-y-4"
              >
                <h1 className="text-4xl lg:text-6xl font-bold leading-tight">
                  {title}
                </h1>
                {subtitle && (
                  <h2 className="text-xl lg:text-2xl text-muted-foreground font-medium">
                    {subtitle}
                  </h2>
                )}
              </motion.div>

              {/* Description */}
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                transition={{ duration: 0.6, delay: 0.5 }}
                className="text-lg text-muted-foreground leading-relaxed max-w-xl"
              >
                {description}
              </motion.p>

              {/* Trust Indicators */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                transition={{ duration: 0.6, delay: 0.6 }}
                className="flex flex-wrap gap-6"
              >
                {trustIndicators.map((indicator, index) => {
                  const IconComponent = indicator.icon;
                  return (
                    <div key={indicator.label} className="flex items-center space-x-2">
                      <IconComponent className="w-5 h-5 text-primary" />
                      <div className="text-sm">
                        <span className="font-semibold text-foreground">{indicator.value}</span>
                        <span className="text-muted-foreground ml-1">{indicator.label}</span>
                      </div>
                    </div>
                  );
                })}
              </motion.div>

              {/* Action Buttons */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
                transition={{ duration: 0.8, delay: 0.7 }}
              >
                {renderActionButtons()}
              </motion.div>

              {/* Social Proof */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                transition={{ duration: 0.6, delay: 0.8 }}
                className="flex items-center space-x-4 text-sm text-muted-foreground"
              >
                <div className="flex items-center space-x-1">
                  {[...Array(5)].map((_, i) => (
                    <Star 
                      key={i} 
                      className={cn(
                        "w-4 h-4",
                        i < Math.floor(rating) ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
                      )} 
                    />
                  ))}
                  <span className="ml-2 font-medium">{rating}/5</span>
                </div>
                <span>•</span>
                <span>Trusted by {userCount} users</span>
                <span>•</span>
                <span>Free 14-day trial</span>
              </motion.div>
            </motion.div>

            {/* Right Column - Hero Image/Video */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: 50 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="relative"
            >
              <div className="relative group">
                {/* Glow Effect */}
                <div className="absolute -inset-4 bg-gradient-to-r from-primary/20 via-primary/10 to-primary/20 rounded-2xl blur-2xl group-hover:blur-3xl transition-all duration-500" />
                
                {/* Main Image Container */}
                <div className="relative bg-gradient-to-br from-background to-muted/30 rounded-2xl p-4 border border-border/50 shadow-2xl">
                  {heroImage?.url ? (
                    <div className="relative overflow-hidden rounded-xl">
                      <Image
                        src={heroImage.url}
                        alt={`${title} - Product Screenshot`}
                        width={600}
                        height={400}
                        className="w-full h-auto object-cover transition-transform duration-500 group-hover:scale-105"
                        priority
                      />
                      
                      {/* Play Button Overlay for Demo */}
                      <div className="absolute inset-0 flex items-center justify-center bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <Button
                          size="lg"
                          onClick={handleDemoClick}
                          className="rounded-full w-16 h-16 p-0 bg-white/90 hover:bg-white text-black shadow-lg"
                        >
                          <Play className="w-6 h-6 ml-1" />
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="w-full h-64 bg-gradient-to-br from-primary/10 to-secondary/10 rounded-xl flex items-center justify-center">
                      <div className="text-center space-y-4">
                        <Sparkles className="w-12 h-12 mx-auto text-primary" />
                        <p className="text-muted-foreground">Product Preview</p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Floating Elements */}
                <motion.div
                  initial={{ opacity: 0, scale: 0 }}
                  animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0 }}
                  transition={{ duration: 0.6, delay: 1 }}
                  className="absolute -top-4 -right-4 bg-green-500 text-white px-3 py-1 rounded-full text-xs font-medium shadow-lg"
                >
                  <CheckCircle className="w-3 h-3 inline mr-1" />
                  Live
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, scale: 0 }}
                  animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0 }}
                  transition={{ duration: 0.6, delay: 1.2 }}
                  className="absolute -bottom-4 -left-4 bg-primary text-primary-foreground px-3 py-1 rounded-full text-xs font-medium shadow-lg"
                >
                  <Award className="w-3 h-3 inline mr-1" />
                  Award Winner
                </motion.div>
              </div>
            </motion.div>
          </div>
        </BackgroundBeamsWithCollision>
      </div>
    </section>
  );
};
