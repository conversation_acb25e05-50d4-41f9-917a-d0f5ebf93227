import { Separator } from "@/components/ui/separator";
import <PERSON> from "next/link";
import Logo from "../logo";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Github,
  Twitter,
  Linkedin,
  Mail,
  Phone,
  MapPin,
  Globe,
  ChevronRight,
  ExternalLink,
  Shield,
  FileText,
  Users,
  Zap
} from "lucide-react";

// Footer navigation data
const footerNavigation = {
  products: [
    { name: "Domestic Solutions", href: "/products?category=domestic" },
    { name: "Commercial Tools", href: "/products?category=commercial" },
    { name: "Creative Suite", href: "/products?category=production-creative" },
    { name: "Custom Development", href: "/products?category=custom-development" },
    { name: "All Products", href: "/products" },
  ],
  company: [
    { name: "About Us", href: "/about" },
    { name: "Our Story", href: "/about#story" },
    { name: "Careers", href: "/careers" },
    { name: "<PERSON> Kit", href: "/press" },
    { name: "Contact", href: "/contact" },
  ],
  support: [
    { name: "Help Center", href: "/help" },
    { name: "Documentation", href: "/docs" },
    { name: "API Reference", href: "/api-docs" },
    { name: "Community", href: "/community" },
    { name: "Status Page", href: "/status" },
  ],
  legal: [
    { name: "Privacy Policy", href: "/privacy" },
    { name: "Terms of Service", href: "/terms" },
    { name: "Cookie Policy", href: "/cookies" },
    { name: "GDPR Compliance", href: "/gdpr" },
    { name: "Security", href: "/security" },
  ],
};

const socialLinks = [
  {
    name: "GitHub",
    href: "https://github.com/futurescapetech",
    icon: Github,
    description: "Follow our open source projects"
  },
  {
    name: "Twitter",
    href: "https://twitter.com/futurescapetech",
    icon: Twitter,
    description: "Latest updates and news"
  },
  {
    name: "LinkedIn",
    href: "https://linkedin.com/company/futurescapetech",
    icon: Linkedin,
    description: "Professional network and insights"
  },
];

const languages = [
  { code: "en", name: "English", flag: "🇺🇸" },
  { code: "hi", name: "हिंदी", flag: "🇮🇳" },
  { code: "es", name: "Español", flag: "🇪🇸" },
];

export const FooterSection = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer
      id="footer"
      className="bg-gradient-to-br from-muted/30 via-background to-muted/20 border-t border-border/50"
    >
      <div className="container py-16 lg:py-20">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8 lg:gap-12">
          {/* Company Info */}
          <div className="lg:col-span-2 space-y-6">
            <div>
              <Logo />
              <p className="mt-4 text-muted-foreground leading-relaxed max-w-sm">
                Empowering businesses with innovative SaaS solutions. From domestic tools to enterprise platforms,
                we deliver technology that scales with your ambitions.
              </p>
            </div>

            {/* Contact Info */}
            <div className="space-y-3">
              <div className="flex items-center space-x-3 text-sm text-muted-foreground">
                <Mail className="w-4 h-4 text-primary" />
                <a href="mailto:<EMAIL>" className="hover:text-foreground transition-colors">
                  <EMAIL>
                </a>
              </div>
              <div className="flex items-center space-x-3 text-sm text-muted-foreground">
                <Phone className="w-4 h-4 text-primary" />
                <a href="tel:******-123-4567" className="hover:text-foreground transition-colors">
                  +****************
                </a>
              </div>
              <div className="flex items-center space-x-3 text-sm text-muted-foreground">
                <MapPin className="w-4 h-4 text-primary" />
                <span>San Francisco, CA</span>
              </div>
            </div>

            {/* Social Links */}
            <div className="space-y-3">
              <h4 className="font-semibold text-foreground">Follow Us</h4>
              <div className="flex space-x-4">
                {socialLinks.map((social) => {
                  const IconComponent = social.icon;
                  return (
                    <a
                      key={social.name}
                      href={social.href}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="group p-2 rounded-lg bg-muted/50 hover:bg-primary hover:text-primary-foreground transition-all duration-300"
                      title={social.description}
                    >
                      <IconComponent className="w-5 h-5" />
                      <span className="sr-only">{social.name}</span>
                    </a>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Products */}
          <div className="space-y-4">
            <h3 className="font-semibold text-foreground flex items-center">
              <Zap className="w-4 h-4 mr-2 text-primary" />
              Products
            </h3>
            <ul className="space-y-3">
              {footerNavigation.products.map((item) => (
                <li key={item.name}>
                  <Link
                    href={item.href}
                    className="text-sm text-muted-foreground hover:text-foreground transition-colors duration-200 flex items-center group"
                  >
                    <ChevronRight className="w-3 h-3 mr-1 opacity-0 group-hover:opacity-100 transition-opacity" />
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Company */}
          <div className="space-y-4">
            <h3 className="font-semibold text-foreground flex items-center">
              <Users className="w-4 h-4 mr-2 text-primary" />
              Company
            </h3>
            <ul className="space-y-3">
              {footerNavigation.company.map((item) => (
                <li key={item.name}>
                  <Link
                    href={item.href}
                    className="text-sm text-muted-foreground hover:text-foreground transition-colors duration-200 flex items-center group"
                  >
                    <ChevronRight className="w-3 h-3 mr-1 opacity-0 group-hover:opacity-100 transition-opacity" />
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Support */}
          <div className="space-y-4">
            <h3 className="font-semibold text-foreground flex items-center">
              <Shield className="w-4 h-4 mr-2 text-primary" />
              Support
            </h3>
            <ul className="space-y-3">
              {footerNavigation.support.map((item) => (
                <li key={item.name}>
                  <Link
                    href={item.href}
                    className="text-sm text-muted-foreground hover:text-foreground transition-colors duration-200 flex items-center group"
                  >
                    <ChevronRight className="w-3 h-3 mr-1 opacity-0 group-hover:opacity-100 transition-opacity" />
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Legal */}
          <div className="space-y-4">
            <h3 className="font-semibold text-foreground flex items-center">
              <FileText className="w-4 h-4 mr-2 text-primary" />
              Legal
            </h3>
            <ul className="space-y-3">
              {footerNavigation.legal.map((item) => (
                <li key={item.name}>
                  <Link
                    href={item.href}
                    className="text-sm text-muted-foreground hover:text-foreground transition-colors duration-200 flex items-center group"
                  >
                    <ChevronRight className="w-3 h-3 mr-1 opacity-0 group-hover:opacity-100 transition-opacity" />
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        <Separator className="my-8" />

        {/* Newsletter Signup */}
        <div className="bg-gradient-to-r from-primary/10 via-primary/5 to-primary/10 rounded-2xl p-6 mb-8">
          <div className="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0">
            <div className="text-center md:text-left">
              <h3 className="font-semibold text-foreground mb-2">Stay Updated</h3>
              <p className="text-sm text-muted-foreground">
                Get the latest product updates, industry insights, and exclusive offers.
              </p>
            </div>
            <div className="flex space-x-3">
              <Button variant="outline" size="sm">
                Subscribe to Newsletter
                <ExternalLink className="w-4 h-4 ml-2" />
              </Button>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="flex flex-col lg:flex-row items-center justify-between space-y-4 lg:space-y-0">
          {/* Copyright */}
          <div className="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-4 text-sm text-muted-foreground">
            <div className="flex items-center space-x-2">
              <span>&copy; {currentYear}</span>
              <Button
                variant="link"
                className="p-0 h-auto text-sm"
                asChild
              >
                <Link
                  href="https://www.futurescapetech.com/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="hover:text-foreground transition-colors"
                >
                  Futurescape Technology
                </Link>
              </Button>
            </div>
            <span className="hidden sm:inline">•</span>
            <span>All rights reserved.</span>
          </div>

          {/* Language Switcher */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <Globe className="w-4 h-4" />
              <span>Language:</span>
            </div>
            <div className="flex items-center space-x-2">
              {languages.map((lang, index) => (
                <div key={lang.code} className="flex items-center">
                  <Button
                    variant="ghost"
                    size="sm"
                    className={`h-8 px-3 text-xs ${
                      lang.code === 'en'
                        ? 'bg-primary/10 text-primary'
                        : 'text-muted-foreground hover:text-foreground'
                    }`}
                    title={`Switch to ${lang.name}`}
                  >
                    <span className="mr-1">{lang.flag}</span>
                    {lang.name}
                  </Button>
                  {index < languages.length - 1 && (
                    <span className="text-muted-foreground/50 mx-1">|</span>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Trust Indicators */}
        <div className="mt-8 pt-6 border-t border-border/50">
          <div className="flex flex-wrap items-center justify-center space-x-6 text-xs text-muted-foreground">
            <div className="flex items-center space-x-2">
              <Shield className="w-4 h-4 text-green-500" />
              <span>SOC 2 Compliant</span>
            </div>
            <div className="flex items-center space-x-2">
              <Shield className="w-4 h-4 text-blue-500" />
              <span>GDPR Ready</span>
            </div>
            <div className="flex items-center space-x-2">
              <Shield className="w-4 h-4 text-purple-500" />
              <span>99.9% Uptime</span>
            </div>
            <div className="flex items-center space-x-2">
              <Users className="w-4 h-4 text-orange-500" />
              <span>50,000+ Users</span>
            </div>
          </div>
        </div>
      </div>

      {/* Structured Data for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Organization",
            "name": "Futurescape Technology",
            "url": "https://www.futurescapetech.com",
            "logo": "https://www.futurescapetech.com/logo.png",
            "description": "Empowering businesses with innovative SaaS solutions. From domestic tools to enterprise platforms, we deliver technology that scales with your ambitions.",
            "contactPoint": {
              "@type": "ContactPoint",
              "telephone": "******-123-4567",
              "contactType": "customer service",
              "email": "<EMAIL>"
            },
            "address": {
              "@type": "PostalAddress",
              "addressLocality": "San Francisco",
              "addressRegion": "CA",
              "addressCountry": "US"
            },
            "sameAs": [
              "https://github.com/futurescapetech",
              "https://twitter.com/futurescapetech",
              "https://linkedin.com/company/futurescapetech"
            ]
          })
        }}
      />
    </footer>
  );
};
