"use client";

import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { prefersReducedMotion } from "./utils";

// Register GSAP plugins
if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger);
}

/**
 * GSAP Animation Utilities
 * Provides consistent animations across the application with accessibility support
 */

/**
 * Default animation settings that respect user preferences
 */
export const getAnimationConfig = (overrides: gsap.TweenVars = {}) => {
  const reducedMotion = prefersReducedMotion();
  
  return {
    duration: reducedMotion ? 0.01 : 0.6,
    ease: reducedMotion ? "none" : "power2.out",
    ...overrides,
  };
};

/**
 * Fade in animation with optional slide
 */
export const fadeIn = (
  element: gsap.TweenTarget,
  options: {
    delay?: number;
    duration?: number;
    y?: number;
    x?: number;
    stagger?: number;
  } = {}
) => {
  const config = getAnimationConfig({
    opacity: 1,
    y: options.y || 0,
    x: options.x || 0,
    delay: options.delay || 0,
    duration: options.duration,
    stagger: options.stagger,
  });

  return gsap.fromTo(
    element,
    {
      opacity: 0,
      y: options.y ? options.y + 30 : 30,
      x: options.x ? options.x + 30 : 0,
    },
    config
  );
};

/**
 * Scale animation for hover effects
 */
export const scaleOnHover = (
  element: gsap.TweenTarget,
  options: {
    scale?: number;
    duration?: number;
  } = {}
) => {
  const config = getAnimationConfig({
    scale: options.scale || 1.05,
    duration: options.duration || 0.3,
    ease: "power2.out",
  });

  const hoverIn = () => gsap.to(element, config);
  const hoverOut = () => gsap.to(element, { ...config, scale: 1 });

  return { hoverIn, hoverOut };
};

/**
 * Line draw animation for SVG paths
 */
export const drawLine = (
  element: gsap.TweenTarget,
  options: {
    duration?: number;
    delay?: number;
    ease?: string;
  } = {}
) => {
  const config = getAnimationConfig({
    strokeDashoffset: 0,
    duration: options.duration || 2,
    delay: options.delay || 0,
    ease: options.ease || "power2.inOut",
  });

  return gsap.fromTo(
    element,
    {
      strokeDasharray: "1000",
      strokeDashoffset: "1000",
    },
    config
  );
};

/**
 * Scroll-triggered animation
 */
export const scrollTriggerAnimation = (
  element: gsap.TweenTarget,
  animation: gsap.TweenVars,
  options: {
    trigger?: string | Element;
    start?: string;
    end?: string;
    scrub?: boolean | number;
    toggleActions?: string;
  } = {}
) => {
  if (prefersReducedMotion()) {
    // If reduced motion is preferred, just set the end state immediately
    gsap.set(element, animation);
    return;
  }

  return gsap.fromTo(
    element,
    {
      opacity: 0,
      y: 50,
    },
    {
      ...animation,
      scrollTrigger: {
        trigger: options.trigger || (element as Element),
        start: options.start || "top 80%",
        end: options.end || "bottom 20%",
        scrub: options.scrub || false,
        toggleActions: options.toggleActions || "play none none reverse",
        ...options,
      },
    }
  );
};

/**
 * Stagger animation for multiple elements
 */
export const staggerAnimation = (
  elements: gsap.TweenTarget,
  options: {
    delay?: number;
    stagger?: number;
    y?: number;
    duration?: number;
  } = {}
) => {
  const config = getAnimationConfig({
    opacity: 1,
    y: 0,
    duration: options.duration || 0.6,
    stagger: options.stagger || 0.1,
    delay: options.delay || 0,
  });

  return gsap.fromTo(
    elements,
    {
      opacity: 0,
      y: options.y || 30,
    },
    config
  );
};

/**
 * Card hover effect with shadow and transform
 */
export const cardHoverEffect = (element: gsap.TweenTarget) => {
  const config = getAnimationConfig({
    duration: 0.3,
    ease: "power2.out",
  });

  const hoverIn = () =>
    gsap.to(element, {
      ...config,
      y: -8,
      scale: 1.02,
      boxShadow: "0 20px 40px rgba(0,0,0,0.1)",
    });

  const hoverOut = () =>
    gsap.to(element, {
      ...config,
      y: 0,
      scale: 1,
      boxShadow: "0 4px 6px rgba(0,0,0,0.05)",
    });

  return { hoverIn, hoverOut };
};

/**
 * Cleanup function for ScrollTrigger instances
 */
export const cleanupScrollTriggers = () => {
  if (typeof window !== "undefined") {
    ScrollTrigger.getAll().forEach((trigger) => trigger.kill());
  }
};

/**
 * Refresh ScrollTrigger (useful after dynamic content changes)
 */
export const refreshScrollTriggers = () => {
  if (typeof window !== "undefined") {
    ScrollTrigger.refresh();
  }
};
