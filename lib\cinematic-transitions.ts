"use client";

import { gsap } from "gsap";
import { Flip } from "gsap/Flip";

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(Flip);
}

// Preloader for destination pages
class PagePreloader {
  private static cache = new Map<string, Promise<string>>();
  
  static async preloadPage(url: string): Promise<string> {
    if (this.cache.has(url)) {
      return this.cache.get(url)!;
    }

    const preloadPromise = fetch(url)
      .then(response => response.text())
      .then(html => {
        // Extract and preload critical resources
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        
        // Preload images
        const images = doc.querySelectorAll('img[src]');
        images.forEach(img => {
          const image = new Image();
          image.src = (img as HTMLImageElement).src;
        });

        return html;
      })
      .catch(error => {
        console.error('Page preload failed:', error);
        throw error;
      });

    this.cache.set(url, preloadPromise);
    return preloadPromise;
  }

  static clearCache() {
    this.cache.clear();
  }
}

// Cinematic transition configuration
const CINEMATIC_CONFIG = {
  cardZoom: {
    scale: 8,
    duration: 0.8,
    ease: "power2.inOut"
  },
  pageReveal: {
    startDelay: 0.4,
    duration: 0.6,
    ease: "power2.out"
  },
  cardFade: {
    startDelay: 0.7,
    duration: 0.3,
    ease: "power2.out"
  }
};

// Create cinematic overlay container
let cinematicContainer: HTMLElement | null = null;

const createCinematicContainer = (): HTMLElement => {
  if (cinematicContainer && document.body.contains(cinematicContainer)) {
    return cinematicContainer;
  }

  cinematicContainer = document.createElement('div');
  cinematicContainer.id = 'cinematic-transition-container';
  cinematicContainer.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    pointer-events: none;
    overflow: hidden;
  `;

  document.body.appendChild(cinematicContainer);
  return cinematicContainer;
};

// Create page preview container with enhanced visual continuity
const createPagePreview = (html: string, targetUrl: string, cardData: any): HTMLElement => {
  const pagePreview = document.createElement('div');
  pagePreview.className = 'cinematic-page-preview';
  pagePreview.style.cssText = `
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100vw;
    height: 100vh;
    transform: translate(-50%, -50%) scale(0.1);
    transform-origin: center center;
    opacity: 0;
    background: ${getComputedStyle(document.body).backgroundColor || 'white'};
    overflow: hidden;
    border-radius: 20px;
    box-shadow: 0 50px 100px rgba(0,0,0,0.3);
  `;

  // Create a simplified preview instead of iframe for better performance
  const previewContent = document.createElement('div');
  previewContent.style.cssText = `
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(var(--muted)) 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    position: relative;
  `;

  // Add hero-like content that matches the card
  const heroSection = document.createElement('div');
  heroSection.style.cssText = `
    text-align: center;
    max-width: 800px;
    z-index: 2;
  `;

  // Product image
  if (cardData.image) {
    const productImage = document.createElement('img');
    productImage.src = cardData.image.src;
    productImage.alt = cardData.image.alt;
    productImage.style.cssText = `
      width: 120px;
      height: 120px;
      object-fit: contain;
      margin-bottom: 24px;
      border-radius: 16px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    `;
    heroSection.appendChild(productImage);
  }

  // Product title
  const title = document.createElement('h1');
  title.textContent = cardData.title;
  title.style.cssText = `
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 16px;
    color: hsl(var(--foreground));
    line-height: 1.2;
  `;
  heroSection.appendChild(title);

  // Subtitle
  const subtitle = document.createElement('p');
  subtitle.textContent = 'Loading product details...';
  subtitle.style.cssText = `
    font-size: 1.25rem;
    color: hsl(var(--muted-foreground));
    margin-bottom: 32px;
    opacity: 0.8;
  `;
  heroSection.appendChild(subtitle);

  // Loading indicator
  const loadingSpinner = document.createElement('div');
  loadingSpinner.style.cssText = `
    width: 40px;
    height: 40px;
    border: 3px solid hsl(var(--muted));
    border-top: 3px solid hsl(var(--primary));
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
  `;
  heroSection.appendChild(loadingSpinner);

  // Background decoration
  const bgDecoration = document.createElement('div');
  bgDecoration.style.cssText = `
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, hsl(var(--primary) / 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, hsl(var(--secondary) / 0.1) 0%, transparent 50%);
    z-index: 1;
  `;

  previewContent.appendChild(bgDecoration);
  previewContent.appendChild(heroSection);
  pagePreview.appendChild(previewContent);

  // Add CSS animation for spinner
  if (!document.querySelector('#cinematic-spinner-styles')) {
    const style = document.createElement('style');
    style.id = 'cinematic-spinner-styles';
    style.textContent = `
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `;
    document.head.appendChild(style);
  }

  return pagePreview;
};

// Extract card visual data for continuity
const extractCardData = (cardElement: HTMLElement) => {
  const rect = cardElement.getBoundingClientRect();
  const image = cardElement.querySelector('img');
  const title = cardElement.querySelector('h3, h2, [class*="title"]');
  
  return {
    rect,
    image: image ? {
      src: (image as HTMLImageElement).src,
      alt: (image as HTMLImageElement).alt
    } : null,
    title: title?.textContent || '',
    backgroundColor: getComputedStyle(cardElement).backgroundColor,
    borderRadius: getComputedStyle(cardElement).borderRadius
  };
};

// Create card clone for animation
const createCardClone = (originalCard: HTMLElement, cardData: any): HTMLElement => {
  const clone = originalCard.cloneNode(true) as HTMLElement;
  clone.style.cssText = `
    position: absolute;
    top: ${cardData.rect.top}px;
    left: ${cardData.rect.left}px;
    width: ${cardData.rect.width}px;
    height: ${cardData.rect.height}px;
    z-index: 10001;
    pointer-events: none;
    transform-origin: center center;
  `;
  
  // Remove any interactive elements from clone
  const buttons = clone.querySelectorAll('button, a');
  buttons.forEach(btn => btn.style.pointerEvents = 'none');
  
  return clone;
};

// Main cinematic transition function
export const cinematicZoomTransition = async (
  cardElement: HTMLElement,
  targetUrl: string,
  productName?: string
): Promise<void> => {
  try {
    // Step 1: Preload destination page
    console.log('🎬 Starting cinematic transition to:', targetUrl);
    const pageHtml = await PagePreloader.preloadPage(targetUrl);
    
    // Step 2: Extract card data and create elements
    const cardData = extractCardData(cardElement);
    const container = createCinematicContainer();
    const cardClone = createCardClone(cardElement, cardData);
    const pagePreview = createPagePreview(pageHtml, targetUrl, cardData);
    
    // Step 3: Setup initial states
    container.appendChild(pagePreview);
    container.appendChild(cardClone);
    
    // Hide original card
    gsap.set(cardElement, { opacity: 0 });
    
    // Step 4: Create cinematic timeline
    const tl = gsap.timeline({
      onComplete: () => {
        // Set flag for destination page
        sessionStorage.setItem('cinematic-transition-active', 'true');
        // Navigate to actual page
        window.location.href = targetUrl;
      }
    });

    // Phase 1: Card zoom in
    tl.to(cardClone, {
      scale: CINEMATIC_CONFIG.cardZoom.scale,
      duration: CINEMATIC_CONFIG.cardZoom.duration,
      ease: CINEMATIC_CONFIG.cardZoom.ease,
      transformOrigin: "center center"
    });

    // Phase 2: Page reveal (starts during card zoom)
    tl.to(pagePreview, {
      opacity: 1,
      scale: 1,
      duration: CINEMATIC_CONFIG.pageReveal.duration,
      ease: CINEMATIC_CONFIG.pageReveal.ease,
      borderRadius: 0
    }, CINEMATIC_CONFIG.pageReveal.startDelay);

    // Phase 3: Card fade out
    tl.to(cardClone, {
      opacity: 0,
      duration: CINEMATIC_CONFIG.cardFade.duration,
      ease: CINEMATIC_CONFIG.cardFade.ease
    }, CINEMATIC_CONFIG.cardFade.startDelay);

    // Phase 4: Background page fade
    tl.to(document.body, {
      filter: "blur(10px)",
      scale: 0.95,
      duration: 0.6,
      ease: "power2.out"
    }, 0);

    // Add loading indicator
    const loadingIndicator = document.createElement('div');
    loadingIndicator.style.cssText = `
      position: absolute;
      bottom: 40px;
      left: 50%;
      transform: translateX(-50%);
      color: white;
      font-size: 14px;
      font-weight: 600;
      z-index: 10002;
      opacity: 0;
    `;
    loadingIndicator.textContent = `Loading ${productName || 'Product'}...`;
    container.appendChild(loadingIndicator);

    tl.to(loadingIndicator, {
      opacity: 1,
      duration: 0.3,
      ease: "power2.out"
    }, 0.5);

  } catch (error) {
    console.error('Cinematic transition failed:', error);
    // Fallback to direct navigation
    window.location.href = targetUrl;
  }
};

// Enhanced card hover with preview hint
export const cinematicCardHover = (cardElement: HTMLElement, isHovering: boolean): void => {
  if (!cardElement) return;

  const image = cardElement.querySelector('img');
  const content = cardElement.querySelector('.card-content, [class*="card"]');
  
  if (isHovering) {
    gsap.to(cardElement, {
      scale: 1.02,
      y: -5,
      boxShadow: "0 25px 50px rgba(0,0,0,0.2)",
      duration: 0.4,
      ease: "power2.out",
    });

    if (image) {
      gsap.to(image, {
        scale: 1.05,
        duration: 0.6,
        ease: "power2.out",
      });
    }

    // Add subtle zoom hint
    const zoomHint = cardElement.querySelector('.zoom-hint') || createZoomHint();
    if (!cardElement.contains(zoomHint)) {
      cardElement.appendChild(zoomHint);
    }
    
    gsap.to(zoomHint, {
      opacity: 1,
      scale: 1,
      duration: 0.3,
      ease: "back.out(1.7)"
    });

  } else {
    gsap.to(cardElement, {
      scale: 1,
      y: 0,
      boxShadow: "0 4px 12px rgba(0,0,0,0.1)",
      duration: 0.4,
      ease: "power2.out",
    });

    if (image) {
      gsap.to(image, {
        scale: 1,
        duration: 0.6,
        ease: "power2.out",
      });
    }

    const zoomHint = cardElement.querySelector('.zoom-hint');
    if (zoomHint) {
      gsap.to(zoomHint, {
        opacity: 0,
        scale: 0.8,
        duration: 0.2,
        ease: "power2.out"
      });
    }
  }
};

// Create zoom hint element
const createZoomHint = (): HTMLElement => {
  const hint = document.createElement('div');
  hint.className = 'zoom-hint';
  hint.style.cssText = `
    position: absolute;
    top: 12px;
    right: 12px;
    width: 32px;
    height: 32px;
    background: rgba(0,0,0,0.7);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    scale: 0.8;
    pointer-events: none;
    z-index: 10;
  `;
  
  hint.innerHTML = `
    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
      <circle cx="11" cy="11" r="8"/>
      <path d="21 21l-4.35-4.35"/>
      <path d="11 8v6"/>
      <path d="8 11h6"/>
    </svg>
  `;
  
  return hint;
};

// Preload pages on hover for instant transitions
export const preloadOnHover = (cardElement: HTMLElement, targetUrl: string): void => {
  let preloadTimeout: NodeJS.Timeout;
  
  const handleMouseEnter = () => {
    preloadTimeout = setTimeout(() => {
      PagePreloader.preloadPage(targetUrl).catch(console.error);
    }, 500); // Preload after 500ms hover
  };
  
  const handleMouseLeave = () => {
    clearTimeout(preloadTimeout);
  };
  
  cardElement.addEventListener('mouseenter', handleMouseEnter);
  cardElement.addEventListener('mouseleave', handleMouseLeave);
  
  // Cleanup function
  return () => {
    cardElement.removeEventListener('mouseenter', handleMouseEnter);
    cardElement.removeEventListener('mouseleave', handleMouseLeave);
    clearTimeout(preloadTimeout);
  };
};

// Initialize cinematic transitions
export const initializeCinematicTransitions = (): void => {
  if (typeof window === 'undefined') return;
  
  // Clean up any existing containers
  const existingContainer = document.getElementById('cinematic-transition-container');
  if (existingContainer) {
    existingContainer.remove();
  }
  
  // Clear preload cache on page unload
  window.addEventListener('beforeunload', () => {
    PagePreloader.clearCache();
  });
  
  console.log('🎬 Cinematic transitions initialized');
};

// Cleanup function
export const cleanupCinematicTransitions = (): void => {
  if (cinematicContainer && document.body.contains(cinematicContainer)) {
    document.body.removeChild(cinematicContainer);
    cinematicContainer = null;
  }
  PagePreloader.clearCache();
};
