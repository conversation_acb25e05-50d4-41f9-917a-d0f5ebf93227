"use client";

import { useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { initializePageTransitions, pageEnterAnimation } from '@/lib/page-transitions';

export function PageTransitionProvider({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();

  useEffect(() => {
    // Initialize page transitions system
    initializePageTransitions();
  }, []);

  useEffect(() => {
    // Run enter animation on route change
    const hasTransitionFlag = sessionStorage.getItem('page-transition-active');
    
    if (hasTransitionFlag) {
      sessionStorage.removeItem('page-transition-active');
      
      // Small delay to ensure page is ready
      const timer = setTimeout(() => {
        pageEnterAnimation();
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [pathname]);

  return <>{children}</>;
}
