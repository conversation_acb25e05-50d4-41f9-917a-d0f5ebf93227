"use client";

import React, { useState, useRef } from "react";
import { motion, AnimatePresence, useInView } from "framer-motion";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, Tabs<PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  CheckCircle,
  ArrowRight,
  Zap,
  Shield,
  Users,
  BarChart3,
  Settings,
  Smartphone,
  Globe,
  Lock,
  Sparkles,
  TrendingUp
} from "lucide-react";
import { cn } from "@/lib/utils";
import SectionHeader from "../section-header";
import SectionContainer from "../section-container";

interface Feature {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  benefits: string[];
  image?: string;
  video?: string;
  stats?: {
    label: string;
    value: string;
    change?: string;
  }[];
}

interface FeatureTab {
  id: string;
  label: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  features: Feature[];
  color: string;
}

interface FeatureTabsProps {
  tabs?: FeatureTab[];
  title?: string;
  description?: string;
}

// Default feature tabs data
const defaultTabs: FeatureTab[] = [
  {
    id: "performance",
    label: "Performance",
    description: "Lightning-fast performance and optimization",
    icon: Zap,
    color: "text-yellow-500",
    features: [
      {
        id: "speed",
        title: "Lightning Fast",
        description: "Experience blazing-fast performance with our optimized infrastructure and advanced caching mechanisms.",
        icon: TrendingUp,
        benefits: [
          "99.9% uptime guarantee",
          "Sub-second response times",
          "Global CDN distribution",
          "Auto-scaling capabilities"
        ],
        image: "/features/performance.jpg",
        stats: [
          { label: "Response Time", value: "< 200ms", change: "+15%" },
          { label: "Uptime", value: "99.9%", change: "+0.1%" },
          { label: "Requests/sec", value: "10,000+", change: "+25%" }
        ]
      }
    ]
  },
  {
    id: "security",
    label: "Security",
    description: "Enterprise-grade security and compliance",
    icon: Shield,
    color: "text-green-500",
    features: [
      {
        id: "encryption",
        title: "Advanced Security",
        description: "Protect your data with military-grade encryption and comprehensive security measures.",
        icon: Lock,
        benefits: [
          "End-to-end encryption",
          "SOC 2 Type II compliance",
          "GDPR compliant",
          "Regular security audits"
        ],
        image: "/features/security.jpg",
        stats: [
          { label: "Encryption", value: "AES-256", change: "Standard" },
          { label: "Compliance", value: "SOC 2", change: "Certified" },
          { label: "Audits", value: "Monthly", change: "Regular" }
        ]
      }
    ]
  },
  {
    id: "collaboration",
    label: "Collaboration",
    description: "Seamless team collaboration and communication",
    icon: Users,
    color: "text-blue-500",
    features: [
      {
        id: "teamwork",
        title: "Team Collaboration",
        description: "Work together seamlessly with real-time collaboration tools and team management features.",
        icon: Users,
        benefits: [
          "Real-time collaboration",
          "Team workspaces",
          "Permission management",
          "Activity tracking"
        ],
        image: "/features/collaboration.jpg",
        stats: [
          { label: "Team Size", value: "Unlimited", change: "Scalable" },
          { label: "Workspaces", value: "Multiple", change: "Flexible" },
          { label: "Permissions", value: "Granular", change: "Secure" }
        ]
      }
    ]
  },
  {
    id: "analytics",
    label: "Analytics",
    description: "Powerful insights and data visualization",
    icon: BarChart3,
    color: "text-purple-500",
    features: [
      {
        id: "insights",
        title: "Advanced Analytics",
        description: "Gain deep insights into your data with powerful analytics and customizable dashboards.",
        icon: BarChart3,
        benefits: [
          "Real-time dashboards",
          "Custom reports",
          "Data visualization",
          "Predictive analytics"
        ],
        image: "/features/analytics.jpg",
        stats: [
          { label: "Data Points", value: "1M+", change: "+50%" },
          { label: "Reports", value: "Custom", change: "Unlimited" },
          { label: "Dashboards", value: "Real-time", change: "Live" }
        ]
      }
    ]
  }
];

export function FeatureTabs({
  tabs = defaultTabs,
  title = "Powerful Features",
  description = "Discover the comprehensive features that make our platform the perfect choice for your business"
}: FeatureTabsProps) {
  const [activeTab, setActiveTab] = useState(tabs[0]?.id || "");
  const sectionRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(sectionRef, { once: true, margin: "-100px" });

  const activeTabData = tabs.find(tab => tab.id === activeTab);
  const activeFeature = activeTabData?.features[0]; // For simplicity, using first feature

  return (
    <SectionContainer ref={sectionRef} className="py-16 lg:py-24">
      {/* Section Header */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
        transition={{ duration: 0.8 }}
      >
        <SectionHeader
          title={title}
          description={description}
        />
      </motion.div>

      {/* Feature Tabs */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          {/* Tab Navigation */}
          <TabsList className="grid w-full grid-cols-2 lg:grid-cols-4 mb-12 h-auto p-1 bg-muted/50">
            {tabs.map((tab) => {
              const IconComponent = tab.icon;
              return (
                <TabsTrigger
                  key={tab.id}
                  value={tab.id}
                  className="flex flex-col items-center space-y-2 p-4 data-[state=active]:bg-background data-[state=active]:shadow-sm"
                >
                  <IconComponent className={cn("w-6 h-6", tab.color)} />
                  <div className="text-center">
                    <div className="font-semibold">{tab.label}</div>
                    <div className="text-xs text-muted-foreground hidden sm:block">
                      {tab.description}
                    </div>
                  </div>
                </TabsTrigger>
              );
            })}
          </TabsList>

          {/* Tab Content */}
          <AnimatePresence mode="wait">
            {tabs.map((tab) => (
              <TabsContent key={tab.id} value={tab.id} className="mt-0">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.5 }}
                >
                  {tab.features.map((feature) => (
                    <div key={feature.id} className="grid lg:grid-cols-2 gap-12 items-center">
                      {/* Feature Content */}
                      <div className="space-y-8">
                        {/* Feature Header */}
                        <div className="space-y-4">
                          <div className="flex items-center space-x-3">
                            <div className={cn(
                              "w-12 h-12 rounded-xl bg-gradient-to-br flex items-center justify-center",
                              tab.id === "performance" && "from-yellow-500 to-orange-500",
                              tab.id === "security" && "from-green-500 to-emerald-500",
                              tab.id === "collaboration" && "from-blue-500 to-cyan-500",
                              tab.id === "analytics" && "from-purple-500 to-pink-500"
                            )}>
                              <feature.icon className="w-6 h-6 text-white" />
                            </div>
                            <div>
                              <h3 className="text-2xl font-bold text-foreground">
                                {feature.title}
                              </h3>
                              <Badge variant="outline" className="mt-1">
                                {tab.label}
                              </Badge>
                            </div>
                          </div>
                          
                          <p className="text-lg text-muted-foreground leading-relaxed">
                            {feature.description}
                          </p>
                        </div>

                        {/* Benefits List */}
                        <div className="space-y-3">
                          <h4 className="font-semibold text-foreground flex items-center">
                            <Sparkles className="w-5 h-5 mr-2 text-primary" />
                            Key Benefits
                          </h4>
                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                            {feature.benefits.map((benefit, index) => (
                              <motion.div
                                key={benefit}
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ duration: 0.3, delay: index * 0.1 }}
                                className="flex items-center space-x-3"
                              >
                                <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                                <span className="text-sm text-muted-foreground">{benefit}</span>
                              </motion.div>
                            ))}
                          </div>
                        </div>

                        {/* Stats */}
                        {feature.stats && (
                          <div className="grid grid-cols-3 gap-4">
                            {feature.stats.map((stat, index) => (
                              <motion.div
                                key={stat.label}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.4, delay: index * 0.1 }}
                                className="text-center p-4 rounded-lg bg-muted/50"
                              >
                                <div className="text-2xl font-bold text-foreground">
                                  {stat.value}
                                </div>
                                <div className="text-sm text-muted-foreground">
                                  {stat.label}
                                </div>
                                {stat.change && (
                                  <div className="text-xs text-green-600 mt-1">
                                    {stat.change}
                                  </div>
                                )}
                              </motion.div>
                            ))}
                          </div>
                        )}

                        {/* CTA */}
                        <div className="flex flex-col sm:flex-row gap-4">
                          <Button size="lg" className="group">
                            Try This Feature
                            <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" />
                          </Button>
                          <Button variant="outline" size="lg">
                            Learn More
                          </Button>
                        </div>
                      </div>

                      {/* Feature Image/Demo */}
                      <div className="relative">
                        <motion.div
                          initial={{ opacity: 0, scale: 0.95 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ duration: 0.6, delay: 0.3 }}
                          className="relative group"
                        >
                          {/* Glow Effect */}
                          <div className={cn(
                            "absolute -inset-4 rounded-2xl blur-2xl opacity-20 group-hover:opacity-30 transition-opacity duration-500",
                            tab.id === "performance" && "bg-gradient-to-r from-yellow-500 to-orange-500",
                            tab.id === "security" && "bg-gradient-to-r from-green-500 to-emerald-500",
                            tab.id === "collaboration" && "bg-gradient-to-r from-blue-500 to-cyan-500",
                            tab.id === "analytics" && "bg-gradient-to-r from-purple-500 to-pink-500"
                          )} />
                          
                          {/* Image Container */}
                          <Card className="overflow-hidden border-border/50 shadow-xl">
                            <CardContent className="p-0">
                              <div className="relative aspect-video">
                                {feature.image ? (
                                  <Image
                                    src={feature.image}
                                    alt={feature.title}
                                    fill
                                    className="object-cover transition-transform duration-500 group-hover:scale-105"
                                    onError={(e) => {
                                      e.currentTarget.src = "/placeholder.jpg";
                                    }}
                                  />
                                ) : (
                                  <div className={cn(
                                    "w-full h-full bg-gradient-to-br flex items-center justify-center",
                                    tab.id === "performance" && "from-yellow-500/10 to-orange-500/10",
                                    tab.id === "security" && "from-green-500/10 to-emerald-500/10",
                                    tab.id === "collaboration" && "from-blue-500/10 to-cyan-500/10",
                                    tab.id === "analytics" && "from-purple-500/10 to-pink-500/10"
                                  )}>
                                    <div className="text-center space-y-4">
                                      <feature.icon className={cn("w-16 h-16 mx-auto", tab.color)} />
                                      <p className="text-muted-foreground font-medium">
                                        {feature.title} Preview
                                      </p>
                                    </div>
                                  </div>
                                )}
                              </div>
                            </CardContent>
                          </Card>
                        </motion.div>
                      </div>
                    </div>
                  ))}
                </motion.div>
              </TabsContent>
            ))}
          </AnimatePresence>
        </Tabs>
      </motion.div>
    </SectionContainer>
  );
}
