"use client";

import { useEffect, useCallback, useRef } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { 
  navigateWithTransition, 
  initializePageTransitions, 
  buttonClickAnimation,
  productCardHoverAnimation 
} from '@/lib/page-transitions';

interface UsePageTransitionsOptions {
  enableTransitions?: boolean;
  enableHoverEffects?: boolean;
  transitionDelay?: number;
}

export function usePageTransitions({
  enableTransitions = true,
  enableHoverEffects = true,
  transitionDelay = 1200
}: UsePageTransitionsOptions = {}) {
  const router = useRouter();
  const pathname = usePathname();
  const isNavigatingRef = useRef(false);

  // Initialize page transitions on mount
  useEffect(() => {
    if (enableTransitions) {
      initializePageTransitions();
    }
  }, [enableTransitions]);

  // Enhanced navigation function with transitions
  const navigateToProduct = useCallback(async (
    productSlug: string, 
    productName?: string,
    options?: {
      delay?: number;
      section?: string;
    }
  ) => {
    if (!enableTransitions || isNavigatingRef.current) {
      // Fallback to regular navigation
      const url = options?.section 
        ? `/products/${productSlug}#${options.section}`
        : `/products/${productSlug}`;
      window.location.href = url;
      return;
    }

    try {
      isNavigatingRef.current = true;
      
      // Set transition flag for the next page
      sessionStorage.setItem('page-transition-active', 'true');
      
      const url = options?.section 
        ? `/products/${productSlug}#${options.section}`
        : `/products/${productSlug}`;
      
      await navigateWithTransition(
        url, 
        productName, 
        options?.delay || transitionDelay
      );
    } catch (error) {
      console.error('Navigation error:', error);
      isNavigatingRef.current = false;
      
      // Fallback navigation
      const url = options?.section 
        ? `/products/${productSlug}#${options.section}`
        : `/products/${productSlug}`;
      window.location.href = url;
    }
  }, [enableTransitions, transitionDelay]);

  // Enhanced navigation for any URL
  const navigateWithAnimation = useCallback(async (
    url: string,
    title?: string,
    options?: {
      delay?: number;
      external?: boolean;
    }
  ) => {
    if (!enableTransitions || isNavigatingRef.current || options?.external) {
      window.location.href = url;
      return;
    }

    try {
      isNavigatingRef.current = true;
      sessionStorage.setItem('page-transition-active', 'true');
      
      await navigateWithTransition(
        url, 
        title, 
        options?.delay || transitionDelay
      );
    } catch (error) {
      console.error('Navigation error:', error);
      isNavigatingRef.current = false;
      window.location.href = url;
    }
  }, [enableTransitions, transitionDelay]);

  // Button click handler with animation
  const handleButtonClick = useCallback((
    event: React.MouseEvent<HTMLElement>,
    action: () => void | Promise<void>
  ) => {
    const element = event.currentTarget;
    
    if (enableTransitions) {
      buttonClickAnimation(element);
    }
    
    // Execute action after animation
    setTimeout(() => {
      action();
    }, 150);
  }, [enableTransitions]);

  // Product card click handler
  const handleProductCardClick = useCallback((
    event: React.MouseEvent<HTMLElement>,
    productSlug: string,
    productName?: string,
    options?: {
      section?: string;
      delay?: number;
    }
  ) => {
    event.preventDefault();
    
    const element = event.currentTarget;
    
    if (enableTransitions) {
      buttonClickAnimation(element);
    }
    
    // Navigate after click animation
    setTimeout(() => {
      navigateToProduct(productSlug, productName, options);
    }, 150);
  }, [navigateToProduct, enableTransitions]);

  // Product card hover handlers
  const handleProductCardHover = useCallback((
    element: HTMLElement,
    isHovering: boolean
  ) => {
    if (enableHoverEffects) {
      productCardHoverAnimation(element, isHovering);
    }
  }, [enableHoverEffects]);

  // Auto-attach event listeners to product cards
  useEffect(() => {
    if (!enableHoverEffects) return;

    const attachHoverListeners = () => {
      const productCards = document.querySelectorAll(
        '[class*="product-card"], .product-item, [data-product-card]'
      );
      
      productCards.forEach((card) => {
        const element = card as HTMLElement;
        
        // Remove existing listeners to prevent duplicates
        element.removeEventListener('mouseenter', element.dataset.hoverEnter as any);
        element.removeEventListener('mouseleave', element.dataset.hoverLeave as any);
        
        // Create new listeners
        const hoverEnter = () => handleProductCardHover(element, true);
        const hoverLeave = () => handleProductCardHover(element, false);
        
        // Store listeners for cleanup
        element.dataset.hoverEnter = hoverEnter as any;
        element.dataset.hoverLeave = hoverLeave as any;
        
        element.addEventListener('mouseenter', hoverEnter);
        element.addEventListener('mouseleave', hoverLeave);
      });
    };

    // Initial attachment
    attachHoverListeners();

    // Re-attach when DOM changes
    const observer = new MutationObserver(() => {
      attachHoverListeners();
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });

    return () => {
      observer.disconnect();
    };
  }, [enableHoverEffects, handleProductCardHover]);

  // Cleanup navigation state on route change
  useEffect(() => {
    isNavigatingRef.current = false;
  }, [pathname]);

  // Utility function to create product slug from name
  const createProductSlug = useCallback((productName: string): string => {
    return productName
      .toLowerCase()
      .replace(/[^\w\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .trim();
  }, []);

  // Enhanced link component props
  const getLinkProps = useCallback((
    href: string,
    title?: string,
    options?: {
      external?: boolean;
      delay?: number;
    }
  ) => {
    return {
      href,
      onClick: (e: React.MouseEvent<HTMLAnchorElement>) => {
        if (!options?.external && enableTransitions) {
          e.preventDefault();
          navigateWithAnimation(href, title, options);
        }
      },
    };
  }, [navigateWithAnimation, enableTransitions]);

  // Product-specific link props
  const getProductLinkProps = useCallback((
    productSlug: string,
    productName?: string,
    options?: {
      section?: string;
      delay?: number;
    }
  ) => {
    const href = options?.section 
      ? `/products/${productSlug}#${options.section}`
      : `/products/${productSlug}`;
    
    return {
      href,
      onClick: (e: React.MouseEvent<HTMLAnchorElement>) => {
        if (enableTransitions) {
          e.preventDefault();
          navigateToProduct(productSlug, productName, options);
        }
      },
    };
  }, [navigateToProduct, enableTransitions]);

  return {
    // Navigation functions
    navigateToProduct,
    navigateWithAnimation,
    
    // Event handlers
    handleButtonClick,
    handleProductCardClick,
    handleProductCardHover,
    
    // Utilities
    createProductSlug,
    getLinkProps,
    getProductLinkProps,
    
    // State
    isNavigating: isNavigatingRef.current,
    
    // Configuration
    enableTransitions,
    enableHoverEffects,
  };
}
