"use client";

import React, { useEffect, useState, useRef } from "react";
import Image from 'next/image';
import Link from 'next/link';
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardFooter, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { PinContainer } from "./3d-pin";
import { ArrowRight, Loader2, Star, ExternalLink } from "lucide-react";
import { motion } from "framer-motion";
import { staggerAnimation, cardHoverEffect } from "@/lib/gsap-utils";
import type { ProductResponse } from "../../@types/product";

// Import Apollo client but with error handling
import { ApolloClient, NormalizedCacheObject, gql } from '@apollo/client';

// Function to get Apollo client dynamically
async function getApolloClient(): Promise<ApolloClient<NormalizedCacheObject> | undefined> {
  try {
    const apolloModule = await import("@/lib/apolloClient");
    return apolloModule.default;
  } catch (error) {
    console.error("Failed to import Apollo client:", error);
    return undefined;
  }
}

// Define the GraphQL query for category-based products
const GET_PRODUCTS_BY_CATEGORY_QUERY = `
  query GetProductsByCategory($category: String) {
    products(filters: { category: { eq: $category } }) {
      slug
      category
      ProductCard {
        productName
        productDescription
        label
        productLogo {
          url
        }
      }
    }
  }
`;

// Define the GraphQL query for all products (fallback)
const GET_ALL_PRODUCTS_QUERY = `
  query GetAllProducts {
    products {
      slug
      category
      ProductCard {
        productName
        productDescription
        label
        productLogo {
          url
        }
      }
    }
  }
`;

interface ProductGridProps {
  category?: string;
  limit?: number;
  showViewAll?: boolean;
  variant?: 'default' | 'compact' | 'featured';
  className?: string;
}

export function ProductGrid({ 
  category, 
  limit = 3, 
  showViewAll = true, 
  variant = 'default',
  className = "" 
}: ProductGridProps) {
  const [products, setProducts] = useState<ProductResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const gridRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const loadProducts = async () => {
      try {
        setLoading(true);
        setError(null);

        // First try to use Apollo Client if available
        const client = await getApolloClient();
        if (client) {
          try {
            const query = category ? GET_PRODUCTS_BY_CATEGORY_QUERY : GET_ALL_PRODUCTS_QUERY;
            const variables = category ? { category } : {};
            
            const { data } = await client.query({
              query: gql`${query}`,
              variables,
              fetchPolicy: "no-cache",
            });
            
            let fetchedProducts = data.products;
            
            // If category filtering didn't work on the server, filter client-side
            if (category && fetchedProducts.length > 0) {
              fetchedProducts = fetchedProducts.filter((product: ProductResponse & { category?: string }) => 
                product.category?.toLowerCase() === category.toLowerCase()
              );
            }
            
            setProducts(fetchedProducts.slice(0, limit));
            return; // Exit early if successful
          } catch (apolloError) {
            console.error('Apollo client error:', apolloError);
            // Fall through to fetch API approach
          }
        }

        // Fallback to standard fetch API if Apollo fails or isn't available
        const apiUrl = process.env.NEXT_PUBLIC_GRAPHQL_API_URL;
        if (!apiUrl) {
          throw new Error('GraphQL API URL not configured');
        }
        
        const query = category ? GET_PRODUCTS_BY_CATEGORY_QUERY : GET_ALL_PRODUCTS_QUERY;
        const variables = category ? { category } : {};
        
        const response = await fetch(apiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            query,
            variables,
          }),
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        if (result.errors) {
          throw new Error(result.errors[0].message);
        }

        let fetchedProducts = result.data.products;
        
        // If category filtering didn't work on the server, filter client-side
        if (category && fetchedProducts.length > 0) {
          fetchedProducts = fetchedProducts.filter((product: ProductResponse & { category?: string }) => 
            product.category?.toLowerCase() === category.toLowerCase()
          );
        }

        setProducts(fetchedProducts.slice(0, limit));
      } catch (error) {
        console.error('Failed to load products:', error);
        setError('Failed to load products. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    loadProducts();
  }, [category, limit]);

  // GSAP animations
  useEffect(() => {
    if (gridRef.current && products.length > 0) {
      const cards = gridRef.current.querySelectorAll('.product-card');
      
      // Stagger animation for cards entrance
      staggerAnimation(cards, {
        delay: 0.2,
        stagger: 0.15,
        y: 40,
        duration: 0.8,
      });

      // Add hover effects to each card
      cards.forEach((card) => {
        const { hoverIn, hoverOut } = cardHoverEffect(card);
        
        card.addEventListener('mouseenter', hoverIn);
        card.addEventListener('mouseleave', hoverOut);
      });

      // Cleanup function
      return () => {
        cards.forEach((card) => {
          card.removeEventListener('mouseenter', () => {});
          card.removeEventListener('mouseleave', () => {});
        });
      };
    }
  }, [products]);

  if (loading) {
    return (
      <div className={`flex justify-center items-center min-h-[300px] ${className}`}>
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (error) {
    return (
      <div className={`text-center text-red-500 my-8 ${className}`}>
        <p>{error}</p>
        <Button 
          variant="outline" 
          onClick={() => window.location.reload()}
          className="mt-4"
        >
          Try Again
        </Button>
      </div>
    );
  }

  if (products.length === 0) {
    return (
      <div className={`text-center text-muted-foreground my-8 ${className}`}>
        <p>No products found{category ? ` in ${category} category` : ''}.</p>
        {showViewAll && (
          <Link href="/products">
            <Button variant="outline" className="mt-4">
              Browse All Products
            </Button>
          </Link>
        )}
      </div>
    );
  }

  const renderProductCard = (product: ProductResponse, index: number) => {
    if (variant === 'featured') {
      return (
        <motion.div
          key={product.slug}
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: index * 0.1 }}
          className="product-card opacity-0"
        >
          <PinContainer title={product.slug} href={`/products/${product.slug}`}>
            <div className="flex basis-full flex-col p-4 tracking-tight text-muted-foreground w-[20rem] h-[16rem]">
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-bold text-base text-foreground line-clamp-1">
                  {product.ProductCard.productName}
                </h3>
                {product.ProductCard.label && (
                  <Badge variant="secondary" className="text-xs">
                    {product.ProductCard.label}
                  </Badge>
                )}
              </div>
              
              <p className="text-sm font-normal mb-4 line-clamp-2">
                {product.ProductCard.productDescription || 'Discover this amazing product'}
              </p>
              
              <div className="flex-1 relative">
                <div className="h-full min-h-[120px] w-full rounded-lg bg-gradient-to-br from-primary/5 via-background to-secondary/5" />
                {product.ProductCard.productLogo && (
                  <div className="absolute inset-0 flex items-center justify-center p-4">
                    <Image 
                      src={product.ProductCard.productLogo.url} 
                      alt={product.ProductCard.productName}
                      width={120}
                      height={120}
                      className="max-h-full max-w-full object-contain"
                    />
                  </div>
                )}
              </div>
            </div>
          </PinContainer>
        </motion.div>
      );
    }

    // Default and compact variants
    return (
      <motion.div
        key={product.slug}
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: index * 0.1 }}
        className="product-card opacity-0"
      >
        <Card className="h-full hover:shadow-lg transition-all duration-300 group">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold text-lg line-clamp-1 group-hover:text-primary transition-colors">
                {product.ProductCard.productName}
              </h3>
              {product.ProductCard.label && (
                <Badge variant="outline" className="text-xs">
                  {product.ProductCard.label}
                </Badge>
              )}
            </div>
          </CardHeader>
          
          <CardContent className="pb-3">
            <p className="text-muted-foreground text-sm line-clamp-2 mb-4">
              {product.ProductCard.productDescription || 'Explore this innovative solution'}
            </p>
            
            {product.ProductCard.productLogo && (
              <div className="flex justify-center mb-4">
                <div className="relative w-16 h-16 rounded-lg bg-gradient-to-br from-primary/10 to-secondary/10 flex items-center justify-center">
                  <Image 
                    src={product.ProductCard.productLogo.url} 
                    alt={product.ProductCard.productName}
                    width={40}
                    height={40}
                    className="object-contain"
                  />
                </div>
              </div>
            )}
          </CardContent>
          
          <CardFooter className="pt-0">
            <Link href={`/products/${product.slug}`} className="w-full">
              <Button variant="outline" size="sm" className="w-full group/btn">
                View Details
                <ExternalLink className="ml-2 h-3 w-3 group-hover/btn:translate-x-0.5 transition-transform" />
              </Button>
            </Link>
          </CardFooter>
        </Card>
      </motion.div>
    );
  };

  return (
    <div className={className}>
      <div 
        ref={gridRef}
        className={`grid gap-6 ${
          variant === 'featured' 
            ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3' 
            : variant === 'compact'
            ? 'grid-cols-2 sm:grid-cols-3 lg:grid-cols-4'
            : 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3'
        }`}
      >
        {products.map((product, index) => renderProductCard(product, index))}
      </div>
      
      {showViewAll && products.length >= limit && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: products.length * 0.1 + 0.2 }}
          className="flex justify-center mt-8"
        >
          <Link href={category ? `/products?category=${category}` : "/products"}>
            <Button variant="outline" size="lg" className="group">
              View All {category ? `${category} ` : ''}Products
              <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
            </Button>
          </Link>
        </motion.div>
      )}
    </div>
  );
}
