"use client";

import React, { useEffect, useState, useRef } from "react";
import Image from 'next/image';
import Link from 'next/link';
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardFooter, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { PinContainer } from "./3d-pin";
import { ArrowRight, Loader2, Star, ExternalLink } from "lucide-react";
import { motion } from "framer-motion";
import { staggerAnimation, cardHoverEffect } from "@/lib/gsap-utils";
import { EnhancedProductCard } from "./enhanced-product-card";
import { usePageTransitions } from "@/hooks/use-page-transitions";
import type { ProductResponse } from "../../@types/product";

// Import Apollo client but with error handling
import { ApolloClient, NormalizedCacheObject, gql } from '@apollo/client';

// Function to get Apollo client dynamically
async function getApolloClient(): Promise<ApolloClient<NormalizedCacheObject> | undefined> {
  try {
    const apolloModule = await import("@/lib/apolloClient");
    return apolloModule.default;
  } catch (error) {
    console.error("Failed to import Apollo client:", error);
    return undefined;
  }
}

// Define the GraphQL query for category-based products
const GET_PRODUCTS_BY_CATEGORY_QUERY = `
  query GetProductsByCategory($category: String) {
    products(filters: { category: { eq: $category } }) {
      slug
      category
      ProductCard {
        productName
        productDescription
        label
        productLogo {
          url
        }
      }
    }
  }
`;

// Define the GraphQL query for all products (fallback)
const GET_ALL_PRODUCTS_QUERY = `
  query GetAllProducts {
    products {
      slug
      category
      ProductCard {
        productName
        productDescription
        label
        productLogo {
          url
        }
      }
    }
  }
`;

interface ProductGridProps {
  category?: string;
  limit?: number;
  showViewAll?: boolean;
  variant?: 'default' | 'compact' | 'featured';
  className?: string;
}

export function ProductGrid({ 
  category, 
  limit = 3, 
  showViewAll = true, 
  variant = 'default',
  className = "" 
}: ProductGridProps) {
  const [products, setProducts] = useState<ProductResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const gridRef = useRef<HTMLDivElement>(null);
  const { enableTransitions } = usePageTransitions();

  useEffect(() => {
    const loadProducts = async () => {
      try {
        setLoading(true);
        setError(null);

        // First try to use Apollo Client if available
        const client = await getApolloClient();
        if (client) {
          try {
            const query = category ? GET_PRODUCTS_BY_CATEGORY_QUERY : GET_ALL_PRODUCTS_QUERY;
            const variables = category ? { category } : {};
            
            const { data } = await client.query({
              query: gql`${query}`,
              variables,
              fetchPolicy: "no-cache",
            });
            
            let fetchedProducts = data.products;
            
            // If category filtering didn't work on the server, filter client-side
            if (category && fetchedProducts.length > 0) {
              fetchedProducts = fetchedProducts.filter((product: ProductResponse & { category?: string }) => 
                product.category?.toLowerCase() === category.toLowerCase()
              );
            }
            
            setProducts(fetchedProducts.slice(0, limit));
            return; // Exit early if successful
          } catch (apolloError) {
            console.error('Apollo client error:', apolloError);
            // Fall through to fetch API approach
          }
        }

        // Fallback to standard fetch API if Apollo fails or isn't available
        const apiUrl = process.env.NEXT_PUBLIC_GRAPHQL_API_URL;
        if (!apiUrl) {
          throw new Error('GraphQL API URL not configured');
        }
        
        const query = category ? GET_PRODUCTS_BY_CATEGORY_QUERY : GET_ALL_PRODUCTS_QUERY;
        const variables = category ? { category } : {};
        
        const response = await fetch(apiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            query,
            variables,
          }),
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        if (result.errors) {
          throw new Error(result.errors[0].message);
        }

        let fetchedProducts = result.data.products;
        
        // If category filtering didn't work on the server, filter client-side
        if (category && fetchedProducts.length > 0) {
          fetchedProducts = fetchedProducts.filter((product: ProductResponse & { category?: string }) => 
            product.category?.toLowerCase() === category.toLowerCase()
          );
        }

        setProducts(fetchedProducts.slice(0, limit));
      } catch (error) {
        console.error('Failed to load products:', error);
        setError('Failed to load products. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    loadProducts();
  }, [category, limit]);

  // GSAP animations
  useEffect(() => {
    if (gridRef.current && products.length > 0) {
      const cards = gridRef.current.querySelectorAll('.product-card');
      
      // Stagger animation for cards entrance
      staggerAnimation(cards, {
        delay: 0.2,
        stagger: 0.15,
        y: 40,
        duration: 0.8,
      });

      // Add hover effects to each card
      cards.forEach((card) => {
        const { hoverIn, hoverOut } = cardHoverEffect(card);
        
        card.addEventListener('mouseenter', hoverIn);
        card.addEventListener('mouseleave', hoverOut);
      });

      // Cleanup function
      return () => {
        cards.forEach((card) => {
          card.removeEventListener('mouseenter', () => {});
          card.removeEventListener('mouseleave', () => {});
        });
      };
    }
  }, [products]);

  if (loading) {
    return (
      <div className={`flex justify-center items-center min-h-[300px] ${className}`}>
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (error) {
    return (
      <div className={`text-center text-red-500 my-8 ${className}`}>
        <p>{error}</p>
        <Button 
          variant="outline" 
          onClick={() => window.location.reload()}
          className="mt-4"
        >
          Try Again
        </Button>
      </div>
    );
  }

  if (products.length === 0) {
    return (
      <div className={`text-center text-muted-foreground my-8 ${className}`}>
        <p>No products found{category ? ` in ${category} category` : ''}.</p>
        {showViewAll && (
          <Link href="/products">
            <Button variant="outline" className="mt-4">
              Browse All Products
            </Button>
          </Link>
        )}
      </div>
    );
  }

  const renderProductCard = (product: ProductResponse, index: number) => {
    // Use the enhanced product card for better transitions and UX
    return (
      <EnhancedProductCard
        key={product.slug}
        product={product}
        variant={variant}
        index={index}
        showRating={true}
        showUsers={variant === 'featured'}
        showDescription={variant !== 'compact'}
        className="h-full"
      />
    );
  };

  return (
    <div className={className}>
      <div 
        ref={gridRef}
        className={`grid gap-6 ${
          variant === 'featured' 
            ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3' 
            : variant === 'compact'
            ? 'grid-cols-2 sm:grid-cols-3 lg:grid-cols-4'
            : 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3'
        }`}
      >
        {products.map((product, index) => renderProductCard(product, index))}
      </div>
      
      {showViewAll && products.length >= limit && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: products.length * 0.1 + 0.2 }}
          className="flex justify-center mt-8"
        >
          <Link href={category ? `/products?category=${category}` : "/products"}>
            <Button variant="outline" size="lg" className="group">
              View All {category ? `${category} ` : ''}Products
              <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
            </Button>
          </Link>
        </motion.div>
      )}
    </div>
  );
}
