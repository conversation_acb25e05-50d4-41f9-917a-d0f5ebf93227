"use client";

import React, { useState, useRef } from "react";
import { motion, AnimatePresence, useInView } from "framer-motion";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  ChevronLeft,
  ChevronRight,
  Maximize2,
  X,
  Monitor,
  Smartphone,
  Tablet,
  Eye,
  Download,
  Share2
} from "lucide-react";
import { cn } from "@/lib/utils";
import SectionHeader from "../section-header";
import SectionContainer from "../section-container";

interface Screenshot {
  id: string;
  url: string;
  title: string;
  description?: string;
  category: "desktop" | "mobile" | "tablet" | "feature";
  featured?: boolean;
}

interface ScreenshotsGalleryProps {
  screenshots?: Screenshot[];
  title?: string;
  description?: string;
}

// Default screenshots data (fallback)
const defaultScreenshots: Screenshot[] = [
  {
    id: "1",
    url: "/screenshots/dashboard.jpg",
    title: "Main Dashboard",
    description: "Comprehensive overview of your data and analytics",
    category: "desktop",
    featured: true
  },
  {
    id: "2",
    url: "/screenshots/mobile-app.jpg",
    title: "Mobile Experience",
    description: "Full-featured mobile application",
    category: "mobile"
  },
  {
    id: "3",
    url: "/screenshots/analytics.jpg",
    title: "Advanced Analytics",
    description: "Deep insights and reporting capabilities",
    category: "desktop"
  },
  {
    id: "4",
    url: "/screenshots/settings.jpg",
    title: "Settings & Configuration",
    description: "Customizable settings for your workflow",
    category: "desktop"
  },
  {
    id: "5",
    url: "/screenshots/tablet-view.jpg",
    title: "Tablet Interface",
    description: "Optimized for tablet devices",
    category: "tablet"
  },
  {
    id: "6",
    url: "/screenshots/feature-highlight.jpg",
    title: "Key Features",
    description: "Highlighting our most powerful features",
    category: "feature",
    featured: true
  }
];

const categoryIcons = {
  desktop: Monitor,
  mobile: Smartphone,
  tablet: Tablet,
  feature: Eye
};

const categoryLabels = {
  desktop: "Desktop",
  mobile: "Mobile",
  tablet: "Tablet",
  feature: "Features"
};

export function ScreenshotsGallery({
  screenshots = defaultScreenshots,
  title = "Product Screenshots",
  description = "Explore our intuitive interface and powerful features across all devices"
}: ScreenshotsGalleryProps) {
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [selectedImage, setSelectedImage] = useState<Screenshot | null>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const sectionRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(sectionRef, { once: true, margin: "-100px" });

  // Filter screenshots based on selected category
  const filteredScreenshots = selectedCategory === "all" 
    ? screenshots 
    : screenshots.filter(screenshot => screenshot.category === selectedCategory);

  // Get unique categories
  const categories = ["all", ...Array.from(new Set(screenshots.map(s => s.category)))];

  const openLightbox = (screenshot: Screenshot) => {
    setSelectedImage(screenshot);
    setCurrentImageIndex(filteredScreenshots.findIndex(s => s.id === screenshot.id));
  };

  const closeLightbox = () => {
    setSelectedImage(null);
  };

  const navigateImage = (direction: "prev" | "next") => {
    const newIndex = direction === "next" 
      ? (currentImageIndex + 1) % filteredScreenshots.length
      : (currentImageIndex - 1 + filteredScreenshots.length) % filteredScreenshots.length;
    
    setCurrentImageIndex(newIndex);
    setSelectedImage(filteredScreenshots[newIndex]);
  };

  const handleDownload = (screenshot: Screenshot) => {
    // In a real implementation, this would trigger a download
    console.log(`Downloading screenshot: ${screenshot.title}`);
  };

  const handleShare = (screenshot: Screenshot) => {
    // In a real implementation, this would open share dialog
    if (navigator.share) {
      navigator.share({
        title: screenshot.title,
        text: screenshot.description,
        url: window.location.href
      });
    }
  };

  return (
    <SectionContainer ref={sectionRef} className="py-16 lg:py-24">
      {/* Section Header */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
        transition={{ duration: 0.8 }}
      >
        <SectionHeader
          title={title}
          description={description}
        />
      </motion.div>

      {/* Category Filter */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
        transition={{ duration: 0.6, delay: 0.2 }}
        className="flex flex-wrap justify-center gap-3 mb-12"
      >
        {categories.map((category) => {
          const IconComponent = category !== "all" ? categoryIcons[category as keyof typeof categoryIcons] : Eye;
          const label = category === "all" ? "All" : categoryLabels[category as keyof typeof categoryLabels];
          
          return (
            <Button
              key={category}
              variant={selectedCategory === category ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedCategory(category)}
              className="group"
            >
              <IconComponent className="w-4 h-4 mr-2" />
              {label}
              <Badge 
                variant="secondary" 
                className="ml-2 text-xs"
              >
                {category === "all" ? screenshots.length : screenshots.filter(s => s.category === category).length}
              </Badge>
            </Button>
          );
        })}
      </motion.div>

      {/* Screenshots Grid */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={isInView ? { opacity: 1 } : { opacity: 0 }}
        transition={{ duration: 0.8, delay: 0.4 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
      >
        <AnimatePresence mode="wait">
          {filteredScreenshots.map((screenshot, index) => {
            const IconComponent = categoryIcons[screenshot.category];
            
            return (
              <motion.div
                key={screenshot.id}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                layout
              >
                <Card className="group overflow-hidden hover:shadow-xl transition-all duration-300 cursor-pointer">
                  <CardContent className="p-0">
                    <div className="relative overflow-hidden">
                      {/* Featured Badge */}
                      {screenshot.featured && (
                        <Badge 
                          className="absolute top-3 left-3 z-10 bg-primary/90 text-primary-foreground"
                        >
                          Featured
                        </Badge>
                      )}

                      {/* Category Icon */}
                      <div className="absolute top-3 right-3 z-10 bg-background/80 backdrop-blur-sm rounded-full p-2">
                        <IconComponent className="w-4 h-4 text-muted-foreground" />
                      </div>

                      {/* Image */}
                      <div className="relative aspect-video overflow-hidden">
                        <Image
                          src={screenshot.url}
                          alt={screenshot.title}
                          fill
                          className="object-cover transition-transform duration-500 group-hover:scale-110"
                          onError={(e) => {
                            e.currentTarget.src = "/placeholder.jpg";
                          }}
                        />
                        
                        {/* Overlay */}
                        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300" />
                        
                        {/* Action Buttons */}
                        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                          <div className="flex space-x-2">
                            <Button
                              size="sm"
                              variant="secondary"
                              onClick={(e) => {
                                e.stopPropagation();
                                openLightbox(screenshot);
                              }}
                              className="bg-white/90 hover:bg-white text-black"
                            >
                              <Maximize2 className="w-4 h-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="secondary"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleShare(screenshot);
                              }}
                              className="bg-white/90 hover:bg-white text-black"
                            >
                              <Share2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      </div>

                      {/* Content */}
                      <div className="p-4">
                        <h3 className="font-semibold text-foreground mb-2 group-hover:text-primary transition-colors">
                          {screenshot.title}
                        </h3>
                        {screenshot.description && (
                          <p className="text-sm text-muted-foreground line-clamp-2">
                            {screenshot.description}
                          </p>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </AnimatePresence>
      </motion.div>

      {/* Lightbox Dialog */}
      <Dialog open={!!selectedImage} onOpenChange={closeLightbox}>
        <DialogContent className="max-w-6xl max-h-[90vh] p-0">
          {selectedImage && (
            <>
              <DialogHeader className="p-6 pb-0">
                <DialogTitle className="flex items-center justify-between">
                  <span>{selectedImage.title}</span>
                  <div className="flex items-center space-x-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleDownload(selectedImage)}
                    >
                      <Download className="w-4 h-4 mr-2" />
                      Download
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleShare(selectedImage)}
                    >
                      <Share2 className="w-4 h-4 mr-2" />
                      Share
                    </Button>
                  </div>
                </DialogTitle>
                {selectedImage.description && (
                  <DialogDescription>
                    {selectedImage.description}
                  </DialogDescription>
                )}
              </DialogHeader>

              <div className="relative flex-1 p-6">
                <div className="relative aspect-video">
                  <Image
                    src={selectedImage.url}
                    alt={selectedImage.title}
                    fill
                    className="object-contain"
                    onError={(e) => {
                      e.currentTarget.src = "/placeholder.jpg";
                    }}
                  />
                </div>

                {/* Navigation Buttons */}
                {filteredScreenshots.length > 1 && (
                  <>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => navigateImage("prev")}
                      className="absolute left-4 top-1/2 -translate-y-1/2 bg-background/80 backdrop-blur-sm"
                    >
                      <ChevronLeft className="w-4 h-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => navigateImage("next")}
                      className="absolute right-4 top-1/2 -translate-y-1/2 bg-background/80 backdrop-blur-sm"
                    >
                      <ChevronRight className="w-4 h-4" />
                    </Button>
                  </>
                )}

                {/* Image Counter */}
                <div className="absolute bottom-4 left-1/2 -translate-x-1/2 bg-background/80 backdrop-blur-sm rounded-full px-3 py-1 text-sm">
                  {currentImageIndex + 1} of {filteredScreenshots.length}
                </div>
              </div>
            </>
          )}
        </DialogContent>
      </Dialog>
    </SectionContainer>
  );
}
