"use client";

import { useEffect, useRef } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { 
  Home, 
  Building2, 
  Palette, 
  Code2,
  ArrowRight 
} from "lucide-react";
import { cn } from "@/lib/utils";
import { cardHoverEffect, staggerAnimation } from "@/lib/gsap-utils";
import { ProductGrid } from "@/components/ui/product-grid";
import { CustomDevelopmentLeadForm } from "@/components/forms/custom-development-lead-form";
import { useCustomDevelopmentForm } from "@/hooks/use-custom-development-form";
import SectionHeader from "../section-header";
import SectionContainer from "../section-container";

interface CategoryCard {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  href: string;
  gradient: string;
  iconColor: string;
}

const categories: CategoryCard[] = [
  {
    id: "domestic",
    title: "Domestic",
    description: "Home and personal productivity solutions for everyday life",
    icon: Home,
    href: "/products?category=domestic",
    gradient: "from-blue-500 to-cyan-500",
    iconColor: "text-blue-600",
  },
  {
    id: "commercial",
    title: "Commercial",
    description: "Business tools and enterprise solutions for growing companies",
    icon: Building2,
    href: "/products?category=commercial",
    gradient: "from-green-500 to-emerald-500",
    iconColor: "text-green-600",
  },
  {
    id: "production-creative",
    title: "Production/Creative",
    description: "Creative tools and production software for content creators",
    icon: Palette,
    href: "/products?category=production-creative",
    gradient: "from-purple-500 to-pink-500",
    iconColor: "text-purple-600",
  },
  {
    id: "custom-development",
    title: "Custom Development",
    description: "Tailored solutions and custom development services",
    icon: Code2,
    href: "/products?category=custom-development",
    gradient: "from-orange-500 to-red-500",
    iconColor: "text-orange-600",
  },
];

export function CategoryCardsSection() {
  const cardsRef = useRef<HTMLDivElement>(null);
  const { isOpen, triggerSource, openForm, closeForm } = useCustomDevelopmentForm({
    defaultSource: 'category-cards-section'
  });

  useEffect(() => {
    if (cardsRef.current) {
      const cards = cardsRef.current.querySelectorAll('.category-card');
      
      // Stagger animation for cards entrance
      staggerAnimation(cards, {
        delay: 0.2,
        stagger: 0.1,
        y: 50,
        duration: 0.8,
      });

      // Add hover effects to each card
      cards.forEach((card) => {
        const { hoverIn, hoverOut } = cardHoverEffect(card);
        
        card.addEventListener('mouseenter', hoverIn);
        card.addEventListener('mouseleave', hoverOut);
      });

      // Cleanup function
      return () => {
        cards.forEach((card) => {
          card.removeEventListener('mouseenter', () => {});
          card.removeEventListener('mouseleave', () => {});
        });
      };
    }
  }, []);

  const handleCategoryClick = (href: string, categoryId?: string) => {
    if (categoryId === 'custom-development') {
      openForm('category-card-custom-development');
    } else {
      window.location.href = href;
    }
  };

  return (
    <SectionContainer id="categories" className="py-16 lg:py-24">
      <SectionHeader
        subTitle="Explore Categories"
        title="Find the Perfect Solution for Your Needs"
        description="Discover our comprehensive range of SaaS products organized by category to help you find exactly what you're looking for."
      />
      
      <div 
        ref={cardsRef}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8 mt-12"
      >
        {categories.map((category) => {
          const IconComponent = category.icon;
          
          return (
            <Card
              key={category.id}
              className={cn(
                "category-card group relative overflow-hidden border-0 bg-white dark:bg-gray-900",
                "cursor-pointer transition-all duration-300 hover:shadow-xl",
                "opacity-0" // Initial state for animation
              )}
              onClick={() => handleCategoryClick(category.href, category.id)}
            >
              {/* Gradient Background */}
              <div 
                className={cn(
                  "absolute inset-0 bg-gradient-to-br opacity-5 group-hover:opacity-10 transition-opacity duration-300",
                  category.gradient
                )}
              />
              
              {/* Content */}
              <CardContent className="relative p-6 h-full flex flex-col">
                {/* Icon */}
                <div className="mb-4">
                  <div className={cn(
                    "w-12 h-12 rounded-lg bg-gradient-to-br flex items-center justify-center",
                    "group-hover:scale-110 transition-transform duration-300",
                    category.gradient
                  )}>
                    <IconComponent className="w-6 h-6 text-white" />
                  </div>
                </div>

                {/* Title */}
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3 group-hover:text-primary transition-colors duration-300">
                  {category.title}
                </h3>

                {/* Description */}
                <p className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed mb-6 flex-grow">
                  {category.description}
                </p>

                {/* CTA Button */}
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-between group-hover:bg-primary/10 transition-colors duration-300"
                >
                  <span>Explore</span>
                  <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
                </Button>
              </CardContent>

              {/* Hover Effect Border */}
              <div className={cn(
                "absolute inset-0 rounded-lg bg-gradient-to-br opacity-0 group-hover:opacity-20",
                "transition-opacity duration-300 pointer-events-none",
                category.gradient
              )} />
            </Card>
          );
        })}
      </div>

      {/* Featured Products by Category */}
      <div className="mt-16 space-y-12">
        <div className="text-center">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            Featured Products by Category
          </h3>
          <p className="text-gray-600 dark:text-gray-300">
            Discover our most popular solutions in each category
          </p>
        </div>

        {/* Domestic Products */}
        <div className="space-y-6">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-blue-500 to-cyan-500 flex items-center justify-center">
              <Home className="w-4 h-4 text-white" />
            </div>
            <h4 className="text-xl font-semibold text-gray-900 dark:text-white">
              Domestic Solutions
            </h4>
          </div>
          <ProductGrid
            category="domestic"
            limit={3}
            variant="featured"
            showViewAll={true}
          />
        </div>

        {/* Commercial Products */}
        <div className="space-y-6">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-green-500 to-emerald-500 flex items-center justify-center">
              <Building2 className="w-4 h-4 text-white" />
            </div>
            <h4 className="text-xl font-semibold text-gray-900 dark:text-white">
              Commercial Solutions
            </h4>
          </div>
          <ProductGrid
            category="commercial"
            limit={3}
            variant="featured"
            showViewAll={true}
          />
        </div>
      </div>

      {/* Additional CTA */}
      <div className="text-center mt-16">
        <p className="text-gray-600 dark:text-gray-300 mb-4">
          Can&apos;t find what you&apos;re looking for?
        </p>
        <Button
          variant="outline"
          size="lg"
          onClick={() => openForm('category-cards-cta')}
          className="group"
        >
          Request Custom Solution
          <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
        </Button>
      </div>

      {/* Custom Development Lead Form */}
      <CustomDevelopmentLeadForm
        isOpen={isOpen}
        onClose={closeForm}
        triggerSource={triggerSource}
      />
    </SectionContainer>
  );
}
