"use client";

import { useState, useCallback } from 'react';

interface UseCustomDevelopmentFormProps {
  defaultSource?: string;
}

export function useCustomDevelopmentForm({ 
  defaultSource = 'unknown' 
}: UseCustomDevelopmentFormProps = {}) {
  const [isOpen, setIsOpen] = useState(false);
  const [triggerSource, setTriggerSource] = useState(defaultSource);

  const openForm = useCallback((source?: string) => {
    if (source) {
      setTriggerSource(source);
    }
    setIsOpen(true);
  }, []);

  const closeForm = useCallback(() => {
    setIsOpen(false);
    // Reset source after a delay to allow for analytics tracking
    setTimeout(() => {
      setTriggerSource(defaultSource);
    }, 1000);
  }, [defaultSource]);

  return {
    isOpen,
    triggerSource,
    openForm,
    closeForm,
  };
}
