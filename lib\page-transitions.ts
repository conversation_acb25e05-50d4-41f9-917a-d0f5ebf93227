"use client";

import { gsap } from "gsap";

// Page transition configuration
const TRANSITION_CONFIG = {
  duration: 0.8,
  ease: "power2.inOut",
  stagger: 0.1,
};

// Transition overlay element
let transitionOverlay: HTMLElement | null = null;

// Create transition overlay if it doesn't exist
const createTransitionOverlay = (): HTMLElement => {
  if (transitionOverlay && document.body.contains(transitionOverlay)) {
    return transitionOverlay;
  }

  transitionOverlay = document.createElement('div');
  transitionOverlay.id = 'page-transition-overlay';
  transitionOverlay.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary) / 0.8) 100%);
    z-index: 9999;
    pointer-events: none;
    opacity: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
  `;

  // Add loading content
  transitionOverlay.innerHTML = `
    <div class="transition-content" style="text-align: center; color: white;">
      <div class="loading-spinner" style="
        width: 40px;
        height: 40px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-top: 3px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 16px;
      "></div>
      <div class="loading-text" style="
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 8px;
        opacity: 0;
      ">Loading Product...</div>
      <div class="loading-subtext" style="
        font-size: 14px;
        opacity: 0.8;
        opacity: 0;
      ">Please wait while we prepare your experience</div>
    </div>
    <style>
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  `;

  document.body.appendChild(transitionOverlay);
  return transitionOverlay;
};

// Page exit animation
export const pageExitAnimation = (callback?: () => void): Promise<void> => {
  return new Promise((resolve) => {
    const overlay = createTransitionOverlay();
    const content = overlay.querySelector('.transition-content') as HTMLElement;
    const text = overlay.querySelector('.loading-text') as HTMLElement;
    const subtext = overlay.querySelector('.loading-subtext') as HTMLElement;

    // Set initial states
    gsap.set(overlay, { opacity: 0, scale: 0.9 });
    gsap.set(content, { y: 30, opacity: 0 });
    gsap.set([text, subtext], { y: 20, opacity: 0 });

    // Create timeline
    const tl = gsap.timeline({
      onComplete: () => {
        if (callback) callback();
        resolve();
      }
    });

    // Animate overlay entrance
    tl.to(overlay, {
      opacity: 1,
      scale: 1,
      duration: TRANSITION_CONFIG.duration * 0.6,
      ease: TRANSITION_CONFIG.ease,
    })
    // Animate content
    .to(content, {
      y: 0,
      opacity: 1,
      duration: TRANSITION_CONFIG.duration * 0.4,
      ease: "power2.out",
    }, "-=0.2")
    // Animate text elements
    .to([text, subtext], {
      y: 0,
      opacity: 1,
      duration: TRANSITION_CONFIG.duration * 0.3,
      stagger: 0.1,
      ease: "power2.out",
    }, "-=0.2");

    // Animate page content out
    const pageElements = document.querySelectorAll('main, header, footer, .page-content');
    if (pageElements.length > 0) {
      gsap.to(pageElements, {
        opacity: 0.3,
        scale: 0.95,
        filter: "blur(5px)",
        duration: TRANSITION_CONFIG.duration * 0.6,
        ease: TRANSITION_CONFIG.ease,
      });
    }
  });
};

// Page enter animation
export const pageEnterAnimation = (): Promise<void> => {
  return new Promise((resolve) => {
    const overlay = createTransitionOverlay();
    
    if (!overlay || !document.body.contains(overlay)) {
      resolve();
      return;
    }

    // Animate overlay exit
    const tl = gsap.timeline({
      onComplete: () => {
        if (overlay && document.body.contains(overlay)) {
          document.body.removeChild(overlay);
        }
        resolve();
      }
    });

    tl.to(overlay, {
      opacity: 0,
      scale: 1.1,
      duration: TRANSITION_CONFIG.duration * 0.6,
      ease: TRANSITION_CONFIG.ease,
    });

    // Animate page content in
    const pageElements = document.querySelectorAll('main, header, footer, .page-content');
    if (pageElements.length > 0) {
      gsap.fromTo(pageElements, 
        {
          opacity: 0,
          scale: 0.95,
          filter: "blur(5px)",
        },
        {
          opacity: 1,
          scale: 1,
          filter: "blur(0px)",
          duration: TRANSITION_CONFIG.duration,
          ease: TRANSITION_CONFIG.ease,
          stagger: TRANSITION_CONFIG.stagger,
        }
      );
    }
  });
};

// Product card hover animation
export const productCardHoverAnimation = (element: HTMLElement, isHovering: boolean): void => {
  if (!element) return;

  const image = element.querySelector('img');
  const content = element.querySelector('.card-content, [class*="card"]');
  const button = element.querySelector('button, .btn, [class*="button"]');

  if (isHovering) {
    gsap.to(element, {
      scale: 1.05,
      y: -10,
      boxShadow: "0 20px 40px rgba(0,0,0,0.15)",
      duration: 0.4,
      ease: "power2.out",
    });

    if (image) {
      gsap.to(image, {
        scale: 1.1,
        duration: 0.6,
        ease: "power2.out",
      });
    }

    if (button) {
      gsap.to(button, {
        backgroundColor: "hsl(var(--primary))",
        color: "hsl(var(--primary-foreground))",
        duration: 0.3,
        ease: "power2.out",
      });
    }
  } else {
    gsap.to(element, {
      scale: 1,
      y: 0,
      boxShadow: "0 4px 12px rgba(0,0,0,0.1)",
      duration: 0.4,
      ease: "power2.out",
    });

    if (image) {
      gsap.to(image, {
        scale: 1,
        duration: 0.6,
        ease: "power2.out",
      });
    }

    if (button) {
      gsap.to(button, {
        backgroundColor: "transparent",
        color: "hsl(var(--foreground))",
        duration: 0.3,
        ease: "power2.out",
      });
    }
  }
};

// Smooth navigation with transition
export const navigateWithTransition = async (
  url: string, 
  productName?: string,
  delay: number = 1200
): Promise<void> => {
  try {
    // Update loading text if product name is provided
    if (productName) {
      const overlay = createTransitionOverlay();
      const loadingText = overlay.querySelector('.loading-text') as HTMLElement;
      if (loadingText) {
        loadingText.textContent = `Loading ${productName}...`;
      }
    }

    // Start exit animation
    await pageExitAnimation();

    // Wait for minimum transition time
    await new Promise(resolve => setTimeout(resolve, delay));

    // Navigate to new page
    window.location.href = url;
  } catch (error) {
    console.error('Navigation transition error:', error);
    // Fallback to direct navigation
    window.location.href = url;
  }
};

// Initialize page transitions on load
export const initializePageTransitions = (): void => {
  // Run enter animation when page loads
  if (typeof window !== 'undefined') {
    // Check if we're coming from a transition
    const hasTransitionFlag = sessionStorage.getItem('page-transition-active');
    
    if (hasTransitionFlag) {
      sessionStorage.removeItem('page-transition-active');
      
      // Delay to ensure page is ready
      setTimeout(() => {
        pageEnterAnimation();
      }, 100);
    }

    // Set up intersection observer for product cards
    const observeProductCards = () => {
      const productCards = document.querySelectorAll('[class*="product-card"], .product-item, [data-product-card]');
      
      productCards.forEach((card) => {
        const element = card as HTMLElement;
        
        element.addEventListener('mouseenter', () => {
          productCardHoverAnimation(element, true);
        });
        
        element.addEventListener('mouseleave', () => {
          productCardHoverAnimation(element, false);
        });
      });
    };

    // Observe product cards after DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', observeProductCards);
    } else {
      observeProductCards();
    }

    // Re-observe when new content is added (for dynamic content)
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as HTMLElement;
              if (element.matches && element.matches('[class*="product-card"], .product-item, [data-product-card]')) {
                element.addEventListener('mouseenter', () => {
                  productCardHoverAnimation(element, true);
                });
                
                element.addEventListener('mouseleave', () => {
                  productCardHoverAnimation(element, false);
                });
              }
            }
          });
        }
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });
  }
};

// Enhanced button click animation
export const buttonClickAnimation = (element: HTMLElement): void => {
  if (!element) return;

  gsap.to(element, {
    scale: 0.95,
    duration: 0.1,
    ease: "power2.out",
    yoyo: true,
    repeat: 1,
  });

  // Add ripple effect
  const ripple = document.createElement('div');
  ripple.style.cssText = `
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple 0.6s linear;
    pointer-events: none;
  `;

  const rect = element.getBoundingClientRect();
  const size = Math.max(rect.width, rect.height);
  ripple.style.width = ripple.style.height = size + 'px';
  ripple.style.left = (rect.width / 2 - size / 2) + 'px';
  ripple.style.top = (rect.height / 2 - size / 2) + 'px';

  element.style.position = 'relative';
  element.style.overflow = 'hidden';
  element.appendChild(ripple);

  setTimeout(() => {
    if (ripple.parentNode) {
      ripple.parentNode.removeChild(ripple);
    }
  }, 600);

  // Add CSS for ripple animation if not exists
  if (!document.querySelector('#ripple-animation-styles')) {
    const style = document.createElement('style');
    style.id = 'ripple-animation-styles';
    style.textContent = `
      @keyframes ripple {
        to {
          transform: scale(4);
          opacity: 0;
        }
      }
    `;
    document.head.appendChild(style);
  }
};
