"use client";

import { AnimatedCard } from "@/components/ui/feature-block-animated-card";
import { 
  Home, 
  Building2, 
  Zap,
  Users,
  Calendar,
  Shield,
  BarChart3,
  Settings,
  Smartphone,
  Laptop,
  Briefcase,
  Target
} from "lucide-react";
import SectionHeader from "../section-header";
import SectionContainer from "../section-container";

interface CategoryData {
  id: string;
  title: string;
  description: string;
  icons: Array<{
    icon: React.ReactNode;
    size: "sm" | "md" | "lg";
    className?: string;
  }>;
}

const categories: CategoryData[] = [
  {
    id: "domestic",
    title: "Domestic",
    description: "Home and personal productivity solutions for everyday life. Smart home management, personal finance, and lifestyle apps.",
    icons: [
      {
        icon: <Home className="h-4 w-4 text-blue-800 dark:text-blue-400" />,
        size: "sm",
      },
      {
        icon: <Smartphone className="h-6 w-6 text-blue-700 dark:text-blue-300" />,
        size: "md",
      },
      {
        icon: <Shield className="h-8 w-8 text-blue-900 dark:text-blue-200" />,
        size: "lg",
      },
      {
        icon: <Calendar className="h-6 w-6 text-blue-700 dark:text-blue-300" />,
        size: "md",
      },
      {
        icon: <Users className="h-4 w-4 text-blue-800 dark:text-blue-400" />,
        size: "sm",
      },
    ],
  },
  {
    id: "commercial",
    title: "Commercial",
    description: "Business tools and enterprise solutions for growing companies. CRM, project management, and team collaboration platforms.",
    icons: [
      {
        icon: <Building2 className="h-4 w-4 text-green-800 dark:text-green-400" />,
        size: "sm",
      },
      {
        icon: <BarChart3 className="h-6 w-6 text-green-700 dark:text-green-300" />,
        size: "md",
      },
      {
        icon: <Briefcase className="h-8 w-8 text-green-900 dark:text-green-200" />,
        size: "lg",
      },
      {
        icon: <Target className="h-6 w-6 text-green-700 dark:text-green-300" />,
        size: "md",
      },
      {
        icon: <Users className="h-4 w-4 text-green-800 dark:text-green-400" />,
        size: "sm",
      },
    ],
  },
  {
    id: "productivity",
    title: "Productivity",
    description: "Boost your efficiency with powerful productivity tools. Task management, automation, and workflow optimization solutions.",
    icons: [
      {
        icon: <Zap className="h-4 w-4 text-purple-800 dark:text-purple-400" />,
        size: "sm",
      },
      {
        icon: <Settings className="h-6 w-6 text-purple-700 dark:text-purple-300" />,
        size: "md",
      },
      {
        icon: <Laptop className="h-8 w-8 text-purple-900 dark:text-purple-200" />,
        size: "lg",
      },
      {
        icon: <BarChart3 className="h-6 w-6 text-purple-700 dark:text-purple-300" />,
        size: "md",
      },
      {
        icon: <Target className="h-4 w-4 text-purple-800 dark:text-purple-400" />,
        size: "sm",
      },
    ],
  },
];

export function ExploreCategoriesSection() {
  const handleCategoryClick = (categoryId: string) => {
    // Navigate to products page with category filter
    window.location.href = `/products?category=${categoryId}`;
  };

  return (
    <SectionContainer id="explore-categories" className="py-16 lg:py-24">
      <SectionHeader
        subTitle="Explore Categories"
        title="Find the Perfect Solution for Your Needs"
        description="Discover our comprehensive range of SaaS products organized by category to help you find exactly what you're looking for."
      />
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-12 mt-12">
        {categories.map((category) => (
          <div
            key={category.id}
            className="cursor-pointer transition-transform duration-300 hover:scale-105"
            onClick={() => handleCategoryClick(category.id)}
          >
            <AnimatedCard
              title={category.title}
              description={category.description}
              icons={category.icons}
              className="h-full hover:shadow-2xl transition-shadow duration-300"
            />
          </div>
        ))}
      </div>

      {/* Additional CTA */}
      <div className="text-center mt-16">
        <p className="text-gray-600 dark:text-gray-300 mb-4">
          Ready to explore all our solutions?
        </p>
        <button
          onClick={() => window.location.href = '/products'}
          className="inline-flex items-center px-6 py-3 bg-primary text-white font-medium rounded-lg hover:bg-primary/90 transition-colors duration-300 group"
        >
          View All Products
          <svg 
            className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>
    </SectionContainer>
  );
}
