"use client";

import { useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { BackgroundBeamsWithCollision } from "@/components/ui/extras/background-beams-with-collision";
import { ArrowRight, Play, Zap, Users, TrendingUp, Shield } from "lucide-react";
import Image from "next/image";
import { motion } from "framer-motion";
import { fadeIn, staggerAnimation } from "@/lib/gsap-utils";

// Ecosystem stats for animated snapshot
const ecosystemStats = [
  { icon: Users, label: "Active Users", value: "50K+", color: "text-blue-500" },
  { icon: Zap, label: "Products", value: "200+", color: "text-green-500" },
  { icon: TrendingUp, label: "Growth Rate", value: "300%", color: "text-purple-500" },
  { icon: Shield, label: "Uptime", value: "99.9%", color: "text-orange-500" },
];

export const HeroSection = () => {
  const statsRef = useRef<HTMLDivElement>(null);
  const ctaRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (statsRef.current) {
      const statCards = statsRef.current.querySelectorAll('.stat-card');
      staggerAnimation(statCards, {
        delay: 1,
        stagger: 0.2,
        y: 30,
        duration: 0.8,
      });
    }

    if (ctaRef.current) {
      fadeIn(ctaRef.current, { delay: 0.8, y: 20 });
    }
  }, []);

  return (
    <section className="container w-full">
      <div className="grid place-items-center lg:max-w-screen-xl mx-auto py-16 md:py-32">
        <BackgroundBeamsWithCollision>
          <div className="text-center space-y-8 pb-20">
            {/* New Badge */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <Badge variant="outline" className="text-sm bg-muted py-2 px-4">
                <span className="mr-2 text-primary">
                  <Badge className="bg-primary text-primary-foreground hover:bg-primary">
                    New
                  </Badge>
                </span>
                <span>AI-Powered SaaS Marketplace is Live!</span>
              </Badge>
            </motion.div>

            {/* Enhanced Title */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="max-w-screen-md mx-auto text-center text-4xl md:text-6xl font-bold"
            >
              <h1>
                <span className="bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent">
                  Smart Solutions
                </span>{" "}
                to Grow Your Business
              </h1>
            </motion.div>

            {/* Enhanced Description */}
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="max-w-screen-sm mx-auto text-xl text-muted-foreground"
            >
              Discover our comprehensive SaaS marketplace with AI-powered solutions
              to streamline your workflow, boost productivity, and accelerate growth.
            </motion.p>

            {/* Ecosystem Stats Snapshot */}
            <motion.div
              ref={statsRef}
              initial={{ opacity: 0, y: 40 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 0.6 }}
              className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto my-8"
            >
              {ecosystemStats.map((stat) => {
                const IconComponent = stat.icon;
                return (
                  <div
                    key={stat.label}
                    className="stat-card opacity-0 bg-background/50 backdrop-blur-sm rounded-lg p-4 border border-border/50"
                  >
                    <IconComponent className={`w-6 h-6 mx-auto mb-2 ${stat.color}`} />
                    <div className="text-2xl font-bold text-foreground">{stat.value}</div>
                    <div className="text-sm text-muted-foreground">{stat.label}</div>
                  </div>
                );
              })}
            </motion.div>

            {/* Enhanced CTAs */}
            <motion.div
              ref={ctaRef}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              className="space-y-4 md:space-y-0 md:space-x-4 opacity-0"
            >
              <Button
                size="lg"
                className="w-5/6 md:w-auto font-bold group/arrow bg-primary hover:bg-primary/90"
                onClick={() => window.location.href = '/products'}
              >
                <Zap className="w-5 h-5 mr-2" />
                Explore Products
                <ArrowRight className="size-5 ml-2 group-hover/arrow:translate-x-1 transition-transform" />
              </Button>

              <Button
                variant="outline"
                size="lg"
                className="w-5/6 md:w-auto font-bold group/play"
                onClick={() => window.location.href = '/trial'}
              >
                <Play className="w-4 h-4 mr-2 group-hover/play:scale-110 transition-transform" />
                Start Free Trial
              </Button>
            </motion.div>
          </div>
        </BackgroundBeamsWithCollision>

        {/* Enhanced Hero Image with Animation */}
        <motion.div
          initial={{ opacity: 0, y: 50, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          transition={{ duration: 1.2, delay: 1 }}
          className="relative group"
        >
          {/* Enhanced blur effect */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 1.5, delay: 1.2 }}
            className="absolute top-2 lg:-top-8 left-1/2 transform -translate-x-1/2 w-[90%] mx-auto h-24 lg:h-80 bg-gradient-to-r from-primary/40 via-blue-500/40 to-purple-500/40 rounded-full blur-3xl"
          />

          {/* Floating elements for ecosystem feel */}
          <motion.div
            animate={{
              y: [0, -10, 0],
              rotate: [0, 5, 0]
            }}
            transition={{
              duration: 6,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="absolute -top-4 -left-4 w-8 h-8 bg-blue-500/20 rounded-full blur-sm"
          />
          <motion.div
            animate={{
              y: [0, 15, 0],
              rotate: [0, -5, 0]
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2
            }}
            className="absolute -top-2 -right-6 w-6 h-6 bg-purple-500/20 rounded-full blur-sm"
          />
          <motion.div
            animate={{
              y: [0, -8, 0],
              x: [0, 5, 0]
            }}
            transition={{
              duration: 7,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 4
            }}
            className="absolute top-1/4 -right-8 w-4 h-4 bg-green-500/20 rounded-full blur-sm"
          />

          <motion.div
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.3 }}
          >
            <Image
              width={1240}
              height={1200}
              className="w-full mx-auto rounded-lg relative leading-none flex items-center dark:hidden shadow-2xl"
              src="/hero-image-light.png"
              alt="SaaS Marketplace Dashboard - Light Mode"
              priority
            />
            <Image
              width={1240}
              height={1200}
              className="w-full mx-auto rounded-lg relative leading-none dark:flex items-center hidden shadow-2xl"
              src="/hero-image-dark.png"
              alt="SaaS Marketplace Dashboard - Dark Mode"
              priority
            />
          </motion.div>

          {/* Enhanced gradient overlay */}
          <div className="absolute bottom-0 left-0 w-full h-20 md:h-32 bg-gradient-to-b from-background/0 via-background/60 to-background rounded-lg"></div>

          {/* Subtle glow effect on hover */}
          <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-primary/5 via-transparent to-blue-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
        </motion.div>
      </div>
    </section>
  );
};
