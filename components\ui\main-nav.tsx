"use client";

import React from "react";
import { Menu, MenuItem, MegaMenuItem, ProductItem, HoveredLink } from "./navbar-menu";
import type { ProductResponse } from "../../@types/product";
import Link from "next/link";

// Main Page Navigation component
export const MainNav = ({
  activeItem,
  setActiveItem,
  products,
  loading,
  routeList,
}: {
  activeItem: string | null;
  setActiveItem: (item: string | null) => void;
  products: ProductResponse[];
  loading: boolean;
  routeList: {href: string; label: string}[];
}) => {
  // Generate navigation items for the main page
  const getNavItems = () => {
    const navItems: React.ReactNode[] = [];
    
    // Always include Products as the first item with enhanced mega menu
    navItems.push(
      <MegaMenuItem
        key="products"
        setActive={setActiveItem}
        active={activeItem}
        item="Products"
        products={products}
        loading={loading}
      />
    );
    
    // For non-product pages, use the standard route items with dropdowns
    routeList.forEach(route => {
      navItems.push(
        <MenuItem 
          key={route.href} 
          setActive={setActiveItem} 
          active={activeItem} 
          item={route.label}
        >
          <div className="flex flex-col space-y-1 py-1">
            <HoveredLink href={route.href}>{route.label}</HoveredLink>
          </div>
        </MenuItem>
      );
    });
    
    return navItems;
  };

  return (
    <Menu 
      setActive={setActiveItem} 
      className="py-0 px-4 bg-transparent border-none shadow-none flex items-center h-10"
    >
      {getNavItems()}
    </Menu>
  );
};

// Generate mobile menu items for main pages
export const getMainMobileMenuItems = (
  routeList: {href: string; label: string}[],
  closeMenu: () => void
) => {
  return routeList.map(({ href, label }) => (
    <Link
      key={href}
      href={href}
      onClick={() => closeMenu()}
      className="block w-full text-left px-3 py-2 hover:bg-slate-100 dark:hover:bg-slate-800 rounded-md transition-colors text-base"
    >
      {label}
    </Link>
  ));
}; 