"use client";

import React, { useRef } from "react";
import { motion } from "framer-motion";
import Image from "next/image";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  ExternalLink, 
  Star, 
  Users, 
  ArrowRight,
  Sparkles,
  Eye
} from "lucide-react";
import { cn } from "@/lib/utils";
import { usePageTransitions } from "@/hooks/use-page-transitions";
import type { ProductResponse } from "../../@types/product";

interface EnhancedProductCardProps {
  product: ProductResponse;
  variant?: "default" | "compact" | "featured" | "grid";
  showRating?: boolean;
  showUsers?: boolean;
  showDescription?: boolean;
  className?: string;
  index?: number;
}

export function EnhancedProductCard({
  product,
  variant = "default",
  showRating = true,
  showUsers = false,
  showDescription = true,
  className,
  index = 0
}: EnhancedProductCardProps) {
  const cardRef = useRef<HTMLDivElement>(null);
  const { 
    handleProductCardClick, 
    handleProductCardHover, 
    createProductSlug,
    enableTransitions 
  } = usePageTransitions();

  const productSlug = product.slug || createProductSlug(product.ProductCard.productName);
  const productName = product.ProductCard.productName;

  // Mock data for demo purposes
  const rating = 4.8;
  const userCount = "2.5k";
  const isPopular = index < 3; // First 3 products are "popular"

  const handleCardClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (enableTransitions) {
      handleProductCardClick(e, productSlug, productName);
    } else {
      window.location.href = `/products/${productSlug}`;
    }
  };

  const handleViewClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    if (enableTransitions) {
      handleProductCardClick(e, productSlug, productName);
    } else {
      window.location.href = `/products/${productSlug}`;
    }
  };

  const handleMouseEnter = () => {
    if (cardRef.current) {
      handleProductCardHover(cardRef.current, true);
    }
  };

  const handleMouseLeave = () => {
    if (cardRef.current) {
      handleProductCardHover(cardRef.current, false);
    }
  };

  if (variant === "featured") {
    return (
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: index * 0.1 }}
        className={cn("product-card", className)}
      >
        <Card 
          ref={cardRef}
          className="group cursor-pointer overflow-hidden border-border/50 hover:border-primary/50 transition-all duration-300 bg-gradient-to-br from-background to-muted/20"
          onClick={handleCardClick}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
          data-product-card
        >
          <CardContent className="p-0">
            {/* Image Section */}
            <div className="relative aspect-video overflow-hidden">
              {product.ProductCard.productLogo?.url ? (
                <Image
                  src={product.ProductCard.productLogo.url}
                  alt={productName}
                  fill
                  className="object-cover transition-transform duration-500 group-hover:scale-110"
                  onError={(e) => {
                    e.currentTarget.src = "/placeholder.jpg";
                  }}
                />
              ) : (
                <div className="w-full h-full bg-gradient-to-br from-primary/10 to-secondary/10 flex items-center justify-center">
                  <Sparkles className="w-12 h-12 text-primary/50" />
                </div>
              )}
              
              {/* Overlay */}
              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300" />
              
              {/* Popular Badge */}
              {isPopular && (
                <Badge className="absolute top-3 left-3 bg-primary/90 text-primary-foreground">
                  <Star className="w-3 h-3 mr-1 fill-current" />
                  Popular
                </Badge>
              )}

              {/* Category Badge */}
              {product.ProductCard.label && (
                <Badge 
                  variant="secondary" 
                  className="absolute top-3 right-3 bg-background/80 backdrop-blur-sm"
                >
                  {product.ProductCard.label}
                </Badge>
              )}

              {/* View Button Overlay */}
              <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <Button
                  size="sm"
                  className="bg-white/90 hover:bg-white text-black shadow-lg"
                  onClick={handleViewClick}
                >
                  <Eye className="w-4 h-4 mr-2" />
                  View Product
                </Button>
              </div>
            </div>

            {/* Content Section */}
            <div className="p-6 space-y-4">
              <div className="space-y-2">
                <h3 className="font-bold text-lg line-clamp-1 group-hover:text-primary transition-colors">
                  {productName}
                </h3>
                
                {showDescription && product.ProductCard.productDescription && (
                  <p className="text-sm text-muted-foreground line-clamp-2">
                    {product.ProductCard.productDescription}
                  </p>
                )}
              </div>

              {/* Stats */}
              <div className="flex items-center justify-between text-sm">
                {showRating && (
                  <div className="flex items-center space-x-1">
                    <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                    <span className="font-medium">{rating}</span>
                  </div>
                )}
                
                {showUsers && (
                  <div className="flex items-center space-x-1 text-muted-foreground">
                    <Users className="w-4 h-4" />
                    <span>{userCount} users</span>
                  </div>
                )}
              </div>

              {/* Action Button */}
              <Button 
                className="w-full group/btn"
                onClick={handleViewClick}
              >
                View Details
                <ArrowRight className="ml-2 w-4 h-4 group-hover/btn:translate-x-1 transition-transform" />
              </Button>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    );
  }

  // Default and compact variants
  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      className={cn("product-card", className)}
    >
      <Card 
        ref={cardRef}
        className="group h-full cursor-pointer hover:shadow-xl transition-all duration-300 border-border/50 hover:border-primary/50"
        onClick={handleCardClick}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        data-product-card
      >
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between space-x-3">
            {/* Product Logo */}
            <div className="flex-shrink-0 w-12 h-12 rounded-lg overflow-hidden bg-gradient-to-br from-primary/10 to-secondary/10">
              {product.ProductCard.productLogo?.url ? (
                <Image
                  src={product.ProductCard.productLogo.url}
                  alt={productName}
                  width={48}
                  height={48}
                  className="w-full h-full object-contain p-1 transition-transform duration-300 group-hover:scale-110"
                  onError={(e) => {
                    e.currentTarget.src = "/placeholder.jpg";
                  }}
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <Sparkles className="w-6 h-6 text-primary/50" />
                </div>
              )}
            </div>

            {/* Title and Badge */}
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-base line-clamp-1 group-hover:text-primary transition-colors">
                {productName}
              </h3>
              
              {product.ProductCard.label && (
                <Badge variant="outline" className="mt-1 text-xs">
                  {product.ProductCard.label}
                </Badge>
              )}
            </div>

            {/* Popular Indicator */}
            {isPopular && (
              <div className="flex-shrink-0">
                <Badge variant="secondary" className="text-xs">
                  <Star className="w-3 h-3 mr-1 fill-current" />
                  Popular
                </Badge>
              </div>
            )}
          </div>
        </CardHeader>

        <CardContent className="pb-3">
          {showDescription && product.ProductCard.productDescription && (
            <p className="text-sm text-muted-foreground line-clamp-3 mb-4">
              {product.ProductCard.productDescription}
            </p>
          )}

          {/* Stats Row */}
          {(showRating || showUsers) && (
            <div className="flex items-center justify-between text-sm mb-4">
              {showRating && (
                <div className="flex items-center space-x-1">
                  <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                  <span className="font-medium">{rating}</span>
                  <span className="text-muted-foreground">(128)</span>
                </div>
              )}
              
              {showUsers && (
                <div className="flex items-center space-x-1 text-muted-foreground">
                  <Users className="w-4 h-4" />
                  <span>{userCount}</span>
                </div>
              )}
            </div>
          )}
        </CardContent>

        <CardFooter className="pt-0">
          <Button 
            variant="outline" 
            size="sm" 
            className="w-full group/btn"
            onClick={handleViewClick}
          >
            View Details
            <ExternalLink className="ml-2 h-3 w-3 group-hover/btn:translate-x-0.5 transition-transform" />
          </Button>
        </CardFooter>
      </Card>
    </motion.div>
  );
}
