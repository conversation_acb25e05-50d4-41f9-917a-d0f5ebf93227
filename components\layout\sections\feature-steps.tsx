"use client";

import { FeatureSteps } from "@/components/ui/feature-section";

const features = [
  { 
    step: 'Step 1', 
    title: 'Choose Your Solution',
    content: 'Browse our marketplace and select the perfect SaaS solution for your business needs.', 
    image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=1200&h=800&fit=crop&crop=center' 
  },
  { 
    step: 'Step 2',
    title: 'Quick Setup',
    content: 'Get started instantly with our streamlined onboarding process and expert support.',
    image: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=1200&h=800&fit=crop&crop=center'
  },
  { 
    step: 'Step 3',
    title: 'Scale & Grow',
    content: 'Watch your business transform with powerful tools designed for growth and efficiency.',
    image: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=1200&h=800&fit=crop&crop=center'
  },
];

export function FeatureStepsSection() {
  return (
    <section className="py-16 lg:py-24 bg-gradient-to-br from-muted/30 via-background to-muted/20">
      <FeatureSteps 
        features={features}
        title="Get Started in 3 Simple Steps"
        autoPlayInterval={4000}
        imageHeight="h-[500px]"
      />
    </section>
  );
}
