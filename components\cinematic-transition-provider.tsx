"use client";

import { useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { initializeCinematicTransitions, cleanupCinematicTransitions } from '@/lib/cinematic-transitions';

interface CinematicTransitionProviderProps {
  children: React.ReactNode;
  enableCinematicMode?: boolean;
}

export function CinematicTransitionProvider({ 
  children, 
  enableCinematicMode = true 
}: CinematicTransitionProviderProps) {
  const pathname = usePathname();

  useEffect(() => {
    if (enableCinematicMode) {
      // Initialize cinematic transitions system
      initializeCinematicTransitions();
      
      // Add global styles for cinematic transitions
      const style = document.createElement('style');
      style.id = 'cinematic-transitions-styles';
      style.textContent = `
        /* Cinematic transition styles */
        .cinematic-card-clone {
          will-change: transform, opacity;
          backface-visibility: hidden;
          perspective: 1000px;
        }
        
        .cinematic-page-preview {
          will-change: transform, opacity, border-radius;
          backface-visibility: hidden;
        }
        
        /* Smooth scrolling for better UX */
        html {
          scroll-behavior: smooth;
        }
        
        /* Optimize for animations */
        [data-product-card] {
          will-change: transform;
          transform: translateZ(0);
        }
        
        /* Zoom hint animation */
        @keyframes zoom-hint-pulse {
          0%, 100% { transform: scale(1); opacity: 0.7; }
          50% { transform: scale(1.1); opacity: 1; }
        }
        
        .zoom-hint {
          animation: zoom-hint-pulse 2s ease-in-out infinite;
        }
        
        /* Loading states */
        .cinematic-loading {
          position: relative;
          overflow: hidden;
        }
        
        .cinematic-loading::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(
            90deg,
            transparent,
            rgba(255, 255, 255, 0.2),
            transparent
          );
          animation: shimmer 1.5s infinite;
        }
        
        @keyframes shimmer {
          0% { left: -100%; }
          100% { left: 100%; }
        }
        
        /* Reduce motion for accessibility */
        @media (prefers-reduced-motion: reduce) {
          .cinematic-card-clone,
          .cinematic-page-preview,
          [data-product-card] {
            will-change: auto;
            transform: none !important;
            transition: none !important;
            animation: none !important;
          }
          
          .zoom-hint {
            animation: none;
          }
          
          .cinematic-loading::before {
            animation: none;
          }
        }
        
        /* High contrast mode support */
        @media (prefers-contrast: high) {
          .zoom-hint {
            background: rgba(0, 0, 0, 0.9);
            border: 2px solid white;
          }
        }
        
        /* Dark mode optimizations */
        @media (prefers-color-scheme: dark) {
          .cinematic-page-preview {
            background: #0a0a0a;
          }
          
          .cinematic-loading::before {
            background: linear-gradient(
              90deg,
              transparent,
              rgba(255, 255, 255, 0.1),
              transparent
            );
          }
        }
      `;
      
      // Remove existing styles if any
      const existingStyle = document.getElementById('cinematic-transitions-styles');
      if (existingStyle) {
        existingStyle.remove();
      }
      
      document.head.appendChild(style);
      
      // Cleanup function
      return () => {
        cleanupCinematicTransitions();
        const styleElement = document.getElementById('cinematic-transitions-styles');
        if (styleElement) {
          styleElement.remove();
        }
      };
    }
  }, [enableCinematicMode]);

  // Handle route changes
  useEffect(() => {
    // Check if we arrived via cinematic transition
    const cinematicFlag = sessionStorage.getItem('cinematic-transition-active');
    
    if (cinematicFlag) {
      sessionStorage.removeItem('cinematic-transition-active');
      
      // Add entrance animation for the new page
      const pageContent = document.querySelector('main, [role="main"], .page-content');
      if (pageContent) {
        const element = pageContent as HTMLElement;
        element.style.opacity = '0';
        element.style.transform = 'scale(0.95)';
        
        // Animate in
        setTimeout(() => {
          element.style.transition = 'opacity 0.6s ease-out, transform 0.6s ease-out';
          element.style.opacity = '1';
          element.style.transform = 'scale(1)';
        }, 100);
      }
    }
  }, [pathname]);

  return <>{children}</>;
}
