"use client";

import React, { useEffect, useRef, useState } from "react";
import { motion, useInView } from "framer-motion";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  Star, 
  Quote, 
  ArrowLeft, 
  ArrowRight,
  TrendingUp,
  Users,
  Award,
  CheckCircle
} from "lucide-react";
import { cn } from "@/lib/utils";
import { scrollTriggerAnimation, staggerAnimation } from "@/lib/gsap-utils";
import SectionHeader from "../section-header";
import SectionContainer from "../section-container";

// Enhanced testimonial data with real SaaS customer reviews
const testimonials = [
  {
    id: 1,
    quote: "This platform has completely transformed how we manage our business operations. The automation features alone have saved us 20+ hours per week.",
    name: "<PERSON>",
    title: "CEO, TechFlow Solutions",
    avatar: "/testimonials/sarah-chen.jpg",
    rating: 5,
    company: "TechFlow Solutions",
    verified: true,
    category: "Commercial"
  },
  {
    id: 2,
    quote: "The customer support is exceptional and the product delivers exactly what it promises. Our productivity has increased by 300% since implementation.",
    name: "<PERSON> Rodriguez",
    title: "Operations Manager, GrowthCorp",
    avatar: "/testimonials/michael-rodriguez.jpg",
    rating: 5,
    company: "GrowthCorp",
    verified: true,
    category: "Commercial"
  },
  {
    id: 3,
    quote: "As a creative professional, I needed tools that could keep up with my workflow. This platform exceeded all my expectations.",
    name: "Emma Thompson",
    title: "Creative Director, PixelStudio",
    avatar: "/testimonials/emma-thompson.jpg",
    rating: 5,
    company: "PixelStudio",
    verified: true,
    category: "Production/Creative"
  },
  {
    id: 4,
    quote: "The custom development team understood our unique requirements perfectly. They delivered a solution that fits our business like a glove.",
    name: "David Park",
    title: "CTO, InnovateLabs",
    avatar: "/testimonials/david-park.jpg",
    rating: 5,
    company: "InnovateLabs",
    verified: true,
    category: "Custom Development"
  },
  {
    id: 5,
    quote: "Simple, powerful, and reliable. This is exactly what we needed for our home office setup. Highly recommend to anyone working remotely.",
    name: "Lisa Johnson",
    title: "Freelance Consultant",
    avatar: "/testimonials/lisa-johnson.jpg",
    rating: 5,
    company: "Independent",
    verified: true,
    category: "Domestic"
  },
  {
    id: 6,
    quote: "The ROI has been incredible. We've seen a 250% increase in efficiency and our team loves using the platform daily.",
    name: "James Wilson",
    title: "VP of Operations, ScaleUp Inc",
    avatar: "/testimonials/james-wilson.jpg",
    rating: 5,
    company: "ScaleUp Inc",
    verified: true,
    category: "Commercial"
  }
];

// Trust indicators and stats
const trustStats = [
  { icon: Users, label: "Happy Customers", value: "50,000+", color: "text-blue-500" },
  { icon: Star, label: "Average Rating", value: "4.9/5", color: "text-yellow-500" },
  { icon: TrendingUp, label: "Growth Rate", value: "300%", color: "text-green-500" },
  { icon: Award, label: "Industry Awards", value: "12+", color: "text-purple-500" },
];

export function EnhancedSocialProofSection() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const sectionRef = useRef<HTMLDivElement>(null);
  const carouselRef = useRef<HTMLDivElement>(null);
  const statsRef = useRef<HTMLDivElement>(null);
  
  // Use Framer Motion's useInView for better scroll detection
  const isInView = useInView(sectionRef, { 
    once: true, 
    margin: "-100px 0px -100px 0px" 
  });

  // Auto-play carousel
  useEffect(() => {
    if (!isAutoPlaying) return;
    
    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % testimonials.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [isAutoPlaying]);

  // GSAP animations when in view
  useEffect(() => {
    if (isInView && sectionRef.current) {
      // Animate stats
      if (statsRef.current) {
        const statCards = statsRef.current.querySelectorAll('.stat-card');
        staggerAnimation(statCards, {
          delay: 0.2,
          stagger: 0.1,
          y: 30,
          duration: 0.8,
        });
      }

      // Animate carousel
      if (carouselRef.current) {
        scrollTriggerAnimation(
          carouselRef.current,
          { opacity: 1, y: 0 },
          { trigger: carouselRef.current, start: "top 80%" }
        );
      }
    }
  }, [isInView]);

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % testimonials.length);
    setIsAutoPlaying(false);
  };

  const prevTestimonial = () => {
    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length);
    setIsAutoPlaying(false);
  };

  const goToTestimonial = (index: number) => {
    setCurrentIndex(index);
    setIsAutoPlaying(false);
  };

  const getInitials = (name: string): string => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  return (
    <SectionContainer 
      ref={sectionRef}
      id="social-proof" 
      className="py-16 lg:py-24 bg-gradient-to-br from-background via-muted/20 to-background"
    >
      {/* Enhanced Section Header with Motion */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
        transition={{ duration: 0.8 }}
      >
        <SectionHeader
          subTitle="Social Proof"
          title={
            <span>
              Trusted by <span className="text-primary">50,000+</span> Businesses Worldwide
            </span>
          }
          description="See what our customers are saying about their experience with our platform"
        />
      </motion.div>

      {/* Trust Stats */}
      <motion.div
        ref={statsRef}
        initial={{ opacity: 0, y: 40 }}
        animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 40 }}
        transition={{ duration: 1, delay: 0.3 }}
        className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto mb-16"
      >
        {trustStats.map((stat, index) => {
          const IconComponent = stat.icon;
          return (
            <div
              key={stat.label}
              className="stat-card opacity-0 text-center p-6 rounded-xl bg-background/50 backdrop-blur-sm border border-border/50 hover:shadow-lg transition-all duration-300"
            >
              <IconComponent className={`w-8 h-8 mx-auto mb-3 ${stat.color}`} />
              <div className="text-2xl font-bold text-foreground mb-1">{stat.value}</div>
              <div className="text-sm text-muted-foreground">{stat.label}</div>
            </div>
          );
        })}
      </motion.div>

      {/* Enhanced Testimonial Carousel */}
      <motion.div
        ref={carouselRef}
        initial={{ opacity: 0, y: 50 }}
        animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
        transition={{ duration: 1, delay: 0.6 }}
        className="relative max-w-4xl mx-auto"
        onMouseEnter={() => setIsAutoPlaying(false)}
        onMouseLeave={() => setIsAutoPlaying(true)}
      >
        {/* Main Testimonial Card */}
        <div className="relative overflow-hidden rounded-2xl">
          <motion.div
            key={currentIndex}
            initial={{ opacity: 0, x: 100 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -100 }}
            transition={{ duration: 0.5, ease: "easeInOut" }}
          >
            <Card className="bg-gradient-to-br from-background to-muted/30 border-border/50 shadow-xl">
              <CardContent className="p-8 md:p-12">
                {/* Quote Icon */}
                <Quote className="w-12 h-12 text-primary/20 mb-6" />
                
                {/* Testimonial Content */}
                <blockquote className="text-xl md:text-2xl font-medium text-foreground leading-relaxed mb-8">
                  "{testimonials[currentIndex].quote}"
                </blockquote>

                {/* Rating */}
                <div className="flex items-center space-x-1 mb-6">
                  {[...Array(testimonials[currentIndex].rating)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>

                {/* Author Info */}
                <div className="flex items-center space-x-4">
                  <Avatar className="w-16 h-16 border-2 border-primary/20">
                    <AvatarImage 
                      src={testimonials[currentIndex].avatar} 
                      alt={testimonials[currentIndex].name}
                    />
                    <AvatarFallback className="bg-primary/10 text-primary font-semibold">
                      {getInitials(testimonials[currentIndex].name)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-1">
                      <h4 className="font-semibold text-foreground text-lg">
                        {testimonials[currentIndex].name}
                      </h4>
                      {testimonials[currentIndex].verified && (
                        <CheckCircle className="w-5 h-5 text-green-500" />
                      )}
                    </div>
                    <p className="text-muted-foreground">
                      {testimonials[currentIndex].title}
                    </p>
                    <div className="flex items-center space-x-2 mt-2">
                      <Badge variant="secondary" className="text-xs">
                        {testimonials[currentIndex].company}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        {testimonials[currentIndex].category}
                      </Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Navigation Controls */}
        <div className="flex items-center justify-between mt-8">
          {/* Previous/Next Buttons */}
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={prevTestimonial}
              className="group"
            >
              <ArrowLeft className="w-4 h-4 group-hover:-translate-x-1 transition-transform" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={nextTestimonial}
              className="group"
            >
              <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
            </Button>
          </div>

          {/* Dot Indicators */}
          <div className="flex space-x-2">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => goToTestimonial(index)}
                className={cn(
                  "w-3 h-3 rounded-full transition-all duration-300",
                  index === currentIndex 
                    ? "bg-primary scale-125" 
                    : "bg-muted-foreground/30 hover:bg-muted-foreground/50"
                )}
              />
            ))}
          </div>

          {/* Auto-play Indicator */}
          <div className="flex items-center space-x-2 text-sm text-muted-foreground">
            <div className={cn(
              "w-2 h-2 rounded-full transition-colors",
              isAutoPlaying ? "bg-green-500" : "bg-gray-400"
            )} />
            <span>{isAutoPlaying ? "Auto" : "Manual"}</span>
          </div>
        </div>
      </motion.div>

      {/* Call to Action */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
        transition={{ duration: 0.8, delay: 1 }}
        className="text-center mt-12"
      >
        <p className="text-muted-foreground mb-6">
          Join thousands of satisfied customers who trust our platform
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button size="lg" className="group">
            Start Your Free Trial
            <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" />
          </Button>
          <Button variant="outline" size="lg">
            View All Reviews
          </Button>
        </div>
      </motion.div>
    </SectionContainer>
  );
}
