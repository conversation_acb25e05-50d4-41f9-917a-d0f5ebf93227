"use client";

import React, { useEffect, useRef, useState } from "react";
import { motion, useInView } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Search,
  CreditCard,
  Rocket,
  ArrowRight,
  CheckCircle,
  Sparkles,
  Clock,
  Shield
} from "lucide-react";
import { cn } from "@/lib/utils";
import { drawLine, staggerAnimation, fadeIn } from "@/lib/gsap-utils";
import { CustomDevelopmentLeadForm } from "@/components/forms/custom-development-lead-form";
import { useCustomDevelopmentForm } from "@/hooks/use-custom-development-form";
import SectionHeader from "../section-header";
import SectionContainer from "../section-container";

// Workflow steps data
const workflowSteps = [
  {
    id: 1,
    title: "Choose Product",
    description: "Browse our comprehensive marketplace and select the perfect SaaS solution for your needs",
    icon: Search,
    color: "from-blue-500 to-cyan-500",
    iconColor: "text-blue-600",
    features: [
      "Browse 200+ products",
      "Filter by category",
      "Compare features",
      "Read reviews"
    ],
    cta: "Explore Products",
    ctaLink: "/products"
  },
  {
    id: 2,
    title: "Select Plan",
    description: "Choose from flexible pricing tiers designed to scale with your business growth",
    icon: CreditCard,
    color: "from-green-500 to-emerald-500",
    iconColor: "text-green-600",
    features: [
      "Flexible pricing tiers",
      "Monthly or yearly billing",
      "Custom enterprise plans",
      "No hidden fees"
    ],
    cta: "View Pricing",
    ctaLink: "/pricing"
  },
  {
    id: 3,
    title: "Start for Free",
    description: "Begin your journey with a free trial and experience the full power of our platform",
    icon: Rocket,
    color: "from-purple-500 to-pink-500",
    iconColor: "text-purple-600",
    features: [
      "14-day free trial",
      "No credit card required",
      "Full feature access",
      "24/7 support included"
    ],
    cta: "Start Free Trial",
    ctaLink: "/trial"
  }
];

// Benefits data
const benefits = [
  {
    icon: Clock,
    title: "Quick Setup",
    description: "Get started in under 5 minutes"
  },
  {
    icon: Shield,
    title: "Secure & Reliable",
    description: "Enterprise-grade security and 99.9% uptime"
  },
  {
    icon: Sparkles,
    title: "No Commitment",
    description: "Cancel anytime, no questions asked"
  }
];

export function SubscriptionWorkflowSection() {
  const [activeStep, setActiveStep] = useState(0);
  const sectionRef = useRef<HTMLDivElement>(null);
  const stepsRef = useRef<HTMLDivElement>(null);
  const linesRef = useRef<HTMLDivElement>(null);
  const benefitsRef = useRef<HTMLDivElement>(null);
  const { isOpen, triggerSource, openForm, closeForm } = useCustomDevelopmentForm({
    defaultSource: 'subscription-workflow-section'
  });
  
  const isInView = useInView(sectionRef, { 
    once: true, 
    margin: "-50px 0px -50px 0px" 
  });

  // Auto-progress through steps for demonstration
  useEffect(() => {
    if (!isInView) return;
    
    const interval = setInterval(() => {
      setActiveStep((prev) => (prev + 1) % workflowSteps.length);
    }, 3000);

    return () => clearInterval(interval);
  }, [isInView]);

  // GSAP animations when in view
  useEffect(() => {
    if (isInView && sectionRef.current) {
      // Animate steps
      if (stepsRef.current) {
        const stepCards = stepsRef.current.querySelectorAll('.workflow-step');
        staggerAnimation(stepCards, {
          delay: 0.3,
          stagger: 0.2,
          y: 50,
          duration: 0.8,
        });
      }

      // Animate connecting lines
      if (linesRef.current) {
        const lines = linesRef.current.querySelectorAll('.connecting-line');
        lines.forEach((line, index) => {
          setTimeout(() => {
            drawLine(line, {
              duration: 1.5,
              delay: 0.5 + (index * 0.3),
            });
          }, 800 + (index * 200));
        });
      }

      // Animate benefits
      if (benefitsRef.current) {
        const benefitCards = benefitsRef.current.querySelectorAll('.benefit-card');
        staggerAnimation(benefitCards, {
          delay: 1.5,
          stagger: 0.1,
          y: 30,
          duration: 0.6,
        });
      }
    }
  }, [isInView]);

  const handleStepClick = (stepIndex: number) => {
    setActiveStep(stepIndex);
  };

  const handleCTAClick = (link: string) => {
    if (link === '/custom-development' || link.includes('custom-development')) {
      openForm('subscription-workflow-cta');
    } else {
      window.location.href = link;
    }
  };

  return (
    <SectionContainer 
      ref={sectionRef}
      id="how-it-works" 
      className="py-16 lg:py-24 bg-gradient-to-br from-muted/30 via-background to-muted/20"
    >
      {/* Section Header */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
        transition={{ duration: 0.8 }}
      >
        <SectionHeader
          subTitle="How It Works"
          title="Get Started in 3 Simple Steps"
          description="Join thousands of businesses who have streamlined their operations with our platform"
        />
      </motion.div>

      {/* Workflow Steps */}
      <div ref={stepsRef} className="relative max-w-6xl mx-auto mb-16">
        {/* Desktop Layout */}
        <div className="hidden lg:block">
          <div className="grid grid-cols-3 gap-8 relative">
            {/* Connecting Lines Container */}
            <div ref={linesRef} className="absolute inset-0 pointer-events-none">
              {/* Line 1 to 2 */}
              <svg 
                className="absolute top-1/2 left-1/3 w-1/3 h-1 -translate-y-1/2"
                viewBox="0 0 100 2"
                preserveAspectRatio="none"
              >
                <path
                  className="connecting-line"
                  d="M 0 1 L 100 1"
                  stroke="currentColor"
                  strokeWidth="2"
                  fill="none"
                  strokeDasharray="100"
                  strokeDashoffset="100"
                  style={{ color: 'rgb(34, 197, 94)' }}
                />
              </svg>
              
              {/* Line 2 to 3 */}
              <svg 
                className="absolute top-1/2 left-2/3 w-1/3 h-1 -translate-y-1/2"
                viewBox="0 0 100 2"
                preserveAspectRatio="none"
              >
                <path
                  className="connecting-line"
                  d="M 0 1 L 100 1"
                  stroke="currentColor"
                  strokeWidth="2"
                  fill="none"
                  strokeDasharray="100"
                  strokeDashoffset="100"
                  style={{ color: 'rgb(168, 85, 247)' }}
                />
              </svg>
            </div>

            {/* Step Cards */}
            {workflowSteps.map((step, index) => {
              const IconComponent = step.icon;
              const isActive = activeStep === index;
              
              return (
                <motion.div
                  key={step.id}
                  className={cn(
                    "workflow-step opacity-0 relative cursor-pointer group",
                    "transition-all duration-300"
                  )}
                  onClick={() => handleStepClick(index)}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Card className={cn(
                    "h-full border-2 transition-all duration-300",
                    isActive 
                      ? "border-primary shadow-lg shadow-primary/20" 
                      : "border-border/50 hover:border-primary/50"
                  )}>
                    <CardContent className="p-6 text-center">
                      {/* Step Number */}
                      <div className="flex justify-center mb-4">
                        <div className={cn(
                          "w-16 h-16 rounded-full flex items-center justify-center text-white font-bold text-lg",
                          "bg-gradient-to-br transition-all duration-300",
                          step.color,
                          isActive ? "scale-110 shadow-lg" : "scale-100"
                        )}>
                          {step.id}
                        </div>
                      </div>

                      {/* Icon */}
                      <div className="flex justify-center mb-4">
                        <div className={cn(
                          "w-12 h-12 rounded-lg bg-gradient-to-br flex items-center justify-center",
                          "transition-all duration-300",
                          step.color,
                          isActive ? "scale-110" : "scale-100"
                        )}>
                          <IconComponent className="w-6 h-6 text-white" />
                        </div>
                      </div>

                      {/* Content */}
                      <h3 className="text-xl font-bold text-foreground mb-3">
                        {step.title}
                      </h3>
                      <p className="text-muted-foreground text-sm mb-6 leading-relaxed">
                        {step.description}
                      </p>

                      {/* Features */}
                      <div className="space-y-2 mb-6">
                        {step.features.map((feature, featureIndex) => (
                          <div key={featureIndex} className="flex items-center text-sm text-muted-foreground">
                            <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                            <span>{feature}</span>
                          </div>
                        ))}
                      </div>

                      {/* CTA Button */}
                      <Button
                        variant={isActive ? "default" : "outline"}
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleCTAClick(step.ctaLink);
                        }}
                        className="group/btn w-full"
                      >
                        {step.cta}
                        <ArrowRight className="ml-2 w-4 h-4 group-hover/btn:translate-x-1 transition-transform" />
                      </Button>
                    </CardContent>
                  </Card>

                  {/* Active Step Indicator */}
                  {isActive && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="absolute -top-2 -right-2 w-6 h-6 bg-primary rounded-full flex items-center justify-center"
                    >
                      <CheckCircle className="w-4 h-4 text-primary-foreground" />
                    </motion.div>
                  )}
                </motion.div>
              );
            })}
          </div>
        </div>

        {/* Mobile Layout */}
        <div className="lg:hidden space-y-6">
          {workflowSteps.map((step, index) => {
            const IconComponent = step.icon;
            const isActive = activeStep === index;
            
            return (
              <motion.div
                key={step.id}
                className="workflow-step opacity-0"
                onClick={() => handleStepClick(index)}
              >
                <Card className={cn(
                  "border-2 transition-all duration-300",
                  isActive 
                    ? "border-primary shadow-lg shadow-primary/20" 
                    : "border-border/50"
                )}>
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      {/* Step Number & Icon */}
                      <div className="flex-shrink-0">
                        <div className={cn(
                          "w-12 h-12 rounded-full flex items-center justify-center text-white font-bold",
                          "bg-gradient-to-br mb-2",
                          step.color
                        )}>
                          {step.id}
                        </div>
                        <div className={cn(
                          "w-12 h-12 rounded-lg bg-gradient-to-br flex items-center justify-center",
                          step.color
                        )}>
                          <IconComponent className="w-6 h-6 text-white" />
                        </div>
                      </div>

                      {/* Content */}
                      <div className="flex-1">
                        <h3 className="text-lg font-bold text-foreground mb-2">
                          {step.title}
                        </h3>
                        <p className="text-muted-foreground text-sm mb-4">
                          {step.description}
                        </p>
                        
                        <div className="grid grid-cols-2 gap-2 mb-4">
                          {step.features.map((feature, featureIndex) => (
                            <div key={featureIndex} className="flex items-center text-xs text-muted-foreground">
                              <CheckCircle className="w-3 h-3 text-green-500 mr-1 flex-shrink-0" />
                              <span>{feature}</span>
                            </div>
                          ))}
                        </div>

                        <Button
                          variant={isActive ? "default" : "outline"}
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleCTAClick(step.ctaLink);
                          }}
                          className="group/btn"
                        >
                          {step.cta}
                          <ArrowRight className="ml-2 w-4 h-4 group-hover/btn:translate-x-1 transition-transform" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </div>
      </div>

      {/* Benefits Section */}
      <motion.div
        ref={benefitsRef}
        initial={{ opacity: 0, y: 40 }}
        animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 40 }}
        transition={{ duration: 1, delay: 1.2 }}
        className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto mb-12"
      >
        {benefits.map((benefit, index) => {
          const IconComponent = benefit.icon;
          return (
            <div
              key={benefit.title}
              className="benefit-card opacity-0 text-center p-6 rounded-xl bg-background/50 backdrop-blur-sm border border-border/50"
            >
              <IconComponent className="w-8 h-8 mx-auto mb-3 text-primary" />
              <h4 className="font-semibold text-foreground mb-2">{benefit.title}</h4>
              <p className="text-sm text-muted-foreground">{benefit.description}</p>
            </div>
          );
        })}
      </motion.div>

      {/* Final CTA */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
        transition={{ duration: 0.8, delay: 1.5 }}
        className="text-center"
      >
        <div className="inline-flex items-center space-x-2 bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-medium mb-6">
          <Sparkles className="w-4 h-4" />
          <span>Ready to get started?</span>
        </div>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button 
            size="lg" 
            className="group"
            onClick={() => handleCTAClick("/trial")}
          >
            Start Your Free Trial
            <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" />
          </Button>
          <Button 
            variant="outline" 
            size="lg"
            onClick={() => handleCTAClick("/products")}
          >
            Browse Products
          </Button>
        </div>
      </motion.div>

      {/* Custom Development Lead Form */}
      <CustomDevelopmentLeadForm
        isOpen={isOpen}
        onClose={closeForm}
        triggerSource={triggerSource}
      />
    </SectionContainer>
  );
}
